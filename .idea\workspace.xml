<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="ALL" />
  </component>
  <component name="CargoProjects">
    <cargoProject FILE="$PROJECT_DIR$/Cargo.toml">
      <package file="$PROJECT_DIR$">
        <enabledFeature name="default" />
      </package>
    </cargoProject>
  </component>
  <component name="ChangeListManager">
    <list default="true" id="e79a5582-334e-4e16-a339-04d504bcaf24" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ExecutionTargetManager" SELECTED_TARGET="RsBuildProfile:dev" />
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MacroExpansionManager">
    <option name="directoryName" value="056KQioM" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 6
}]]></component>
  <component name="ProjectId" id="2zw4ikjI41PQxdWC4NPaP5mY2tZ" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Cargo.Run pathfinding.executor": "Debug",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "RunOnceActivity.rust.reset.selective.auto.import": "true",
    "git-widget-placeholder": "master",
    "ignore.virus.scanning.warn.message": "true",
    "last_opened_file_path": "D:/crscu/route_palnning/codes/rust/pathfinding",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "org.rust.cargo.project.model.PROJECT_DISCOVERY": "true",
    "org.rust.cargo.project.model.impl.CargoExternalSystemProjectAware.subscribe.first.balloon": "",
    "org.rust.first.attach.projects": "true",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager" selected="Cargo.Run pathfinding">
    <configuration name="Run pathfinding" type="CargoCommandRunConfiguration" factoryName="Cargo Command">
      <option name="buildProfileId" value="dev" />
      <option name="command" value="run --package pathfinding --bin pathfinding" />
      <option name="workingDirectory" value="file://$PROJECT_DIR$" />
      <envs />
      <option name="emulateTerminal" value="true" />
      <option name="channel" value="DEFAULT" />
      <option name="requiredFeatures" value="true" />
      <option name="allFeatures" value="false" />
      <option name="withSudo" value="false" />
      <option name="buildTarget" value="REMOTE" />
      <option name="backtrace" value="SHORT" />
      <option name="isRedirectInput" value="false" />
      <option name="redirectInputPath" value="" />
      <method v="2">
        <option name="CARGO.BUILD_TASK_PROVIDER" enabled="true" />
      </method>
    </configuration>
    <configuration name="Test pathfinding" type="CargoCommandRunConfiguration" factoryName="Cargo Command">
      <option name="command" value="test --workspace" />
      <option name="workingDirectory" value="file://$PROJECT_DIR$" />
      <envs />
      <option name="emulateTerminal" value="true" />
      <option name="channel" value="DEFAULT" />
      <option name="requiredFeatures" value="true" />
      <option name="allFeatures" value="false" />
      <option name="withSudo" value="false" />
      <option name="buildTarget" value="REMOTE" />
      <option name="backtrace" value="SHORT" />
      <option name="isRedirectInput" value="false" />
      <option name="redirectInputPath" value="" />
      <method v="2">
        <option name="CARGO.BUILD_TASK_PROVIDER" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="RustProjectSettings">
    <option name="toolchainHomeDirectory" value="$USER_HOME$/.cargo/bin" />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="e79a5582-334e-4e16-a339-04d504bcaf24" name="更改" comment="" />
      <created>1752628930572</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752628930572</updated>
      <workItem from="1752628931785" duration="3852000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/src/pathfinding.rs</url>
          <line>201</line>
          <option name="timeStamp" value="6" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/src/pathfinding.rs</url>
          <line>429</line>
          <option name="timeStamp" value="11" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/src/pathfinding.rs</url>
          <line>256</line>
          <option name="timeStamp" value="17" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/src/pathfinding.rs</url>
          <line>245</line>
          <option name="timeStamp" value="18" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/src/pathfinding.rs</url>
          <line>269</line>
          <option name="timeStamp" value="19" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>