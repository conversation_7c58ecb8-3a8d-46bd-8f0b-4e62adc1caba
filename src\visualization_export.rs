use crate::types::{GridCoord, Path, MapConfig};
use crate::obstacles::StaticObstacleMap;
use serde::{Deserialize, Serialize};
use std::fs::File;
use std::io::Write;

/// 可视化数据结构
#[derive(Debug, Serialize, Deserialize)]
pub struct VisualizationData {
    pub map_config: MapConfig,
    pub obstacles: Vec<GridCoord>,
    pub path: Option<Vec<GridCoord>>,
    pub start_point: Option<GridCoord>,
    pub goal_point: Option<GridCoord>,
    pub planning_time_ms: f32,
    pub path_length: f32,
}

/// 可视化数据导出器
pub struct VisualizationExporter {
    data: VisualizationData,
}

impl VisualizationExporter {
    pub fn new(config: MapConfig) -> Self {
        Self {
            data: VisualizationData {
                map_config: config,
                obstacles: Vec::new(),
                path: None,
                start_point: None,
                goal_point: None,
                planning_time_ms: 0.0,
                path_length: 0.0,
            },
        }
    }

    /// 添加障碍物数据
    pub fn add_obstacles(&mut self, obstacle_map: &StaticObstacleMap) {
        self.data.obstacles.clear();
        
        for x in 0..self.data.map_config.width {
            for y in 0..self.data.map_config.height {
                for z in 0..self.data.map_config.depth {
                    let coord = GridCoord::new(x, y, z);
                    if obstacle_map.is_obstacle(&coord) {
                        self.data.obstacles.push(coord);
                    }
                }
            }
        }
        
        println!("导出了 {} 个障碍物", self.data.obstacles.len());
    }

    /// 添加路径数据
    pub fn add_path(&mut self, path: &Path, start: GridCoord, goal: GridCoord) {
        self.data.path = Some(path.waypoints.clone());
        self.data.start_point = Some(start);
        self.data.goal_point = Some(goal);
        self.data.planning_time_ms = path.planning_time_ms;
        self.data.path_length = path.total_cost;
        
        println!("导出了路径，包含 {} 个路径点", path.waypoints.len());
    }

    /// 保存为JSON格式
    pub fn save_json(&self, filename: &str) -> Result<(), Box<dyn std::error::Error>> {
        let json_data = serde_json::to_string_pretty(&self.data)?;
        let mut file = File::create(filename)?;
        file.write_all(json_data.as_bytes())?;
        println!("数据已保存到: {}", filename);
        Ok(())
    }

    /// 保存为CSV格式（用于简单分析）
    pub fn save_csv(&self, base_filename: &str) -> Result<(), Box<dyn std::error::Error>> {
        // 保存障碍物
        let obstacles_filename = format!("{}_obstacles.csv", base_filename);
        let mut file = File::create(&obstacles_filename)?;
        writeln!(file, "x,y,z")?;
        for obstacle in &self.data.obstacles {
            writeln!(file, "{},{},{}", obstacle.x, obstacle.y, obstacle.z)?;
        }
        println!("障碍物数据已保存到: {}", obstacles_filename);

        // 保存路径
        if let Some(ref path) = self.data.path {
            let path_filename = format!("{}_path.csv", base_filename);
            let mut file = File::create(&path_filename)?;
            writeln!(file, "x,y,z,step")?;
            for (i, point) in path.iter().enumerate() {
                writeln!(file, "{},{},{},{}", point.x, point.y, point.z, i)?;
            }
            println!("路径数据已保存到: {}", path_filename);
        }

        // 保存统计信息
        let stats_filename = format!("{}_stats.csv", base_filename);
        let mut file = File::create(&stats_filename)?;
        writeln!(file, "metric,value")?;
        writeln!(file, "map_width,{}", self.data.map_config.width)?;
        writeln!(file, "map_height,{}", self.data.map_config.height)?;
        writeln!(file, "map_depth,{}", self.data.map_config.depth)?;
        writeln!(file, "obstacle_count,{}", self.data.obstacles.len())?;
        writeln!(file, "planning_time_ms,{}", self.data.planning_time_ms)?;
        writeln!(file, "path_length,{}", self.data.path_length)?;
        if let Some(ref path) = self.data.path {
            writeln!(file, "path_points,{}", path.len())?;
        }
        println!("统计信息已保存到: {}", stats_filename);

        Ok(())
    }

    /// 生成Python可视化脚本
    pub fn generate_python_script(&self, script_filename: &str, data_filename: &str) -> Result<(), Box<dyn std::error::Error>> {
        let script_content = format!(r#"#!/usr/bin/env python3
"""
无人机3D路径规划可视化脚本
自动生成的Python脚本，用于可视化路径规划结果
"""

import json
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

def load_data(filename):
    """加载路径规划数据"""
    with open(filename, 'r', encoding='utf-8') as f:
        return json.load(f)

def visualize_3d(data):
    """创建3D可视化"""
    fig = plt.figure(figsize=(15, 10))
    
    # 3D视图
    ax1 = fig.add_subplot(221, projection='3d')
    
    # 绘制障碍物
    if data['obstacles']:
        obstacles = np.array([[obs['x'], obs['y'], obs['z']] for obs in data['obstacles']])
        ax1.scatter(obstacles[:, 0], obstacles[:, 1], obstacles[:, 2], 
                   c='red', marker='s', s=20, alpha=0.6, label='障碍物')
    
    # 绘制路径
    if data['path']:
        path = np.array([[p['x'], p['y'], p['z']] for p in data['path']])
        ax1.plot(path[:, 0], path[:, 1], path[:, 2], 
                'b-', linewidth=2, label='路径')
        ax1.scatter(path[:, 0], path[:, 1], path[:, 2], 
                   c='blue', s=30, alpha=0.8)
        
        # 标记起点和终点
        if data['start_point']:
            start = data['start_point']
            ax1.scatter([start['x']], [start['y']], [start['z']], 
                       c='green', s=100, marker='o', label='起点')
        
        if data['goal_point']:
            goal = data['goal_point']
            ax1.scatter([goal['x']], [goal['y']], [goal['z']], 
                       c='orange', s=100, marker='*', label='终点')
    
    ax1.set_xlabel('X')
    ax1.set_ylabel('Y')
    ax1.set_zlabel('Z')
    ax1.set_title('3D路径规划结果')
    ax1.legend()
    
    # XY平面投影
    ax2 = fig.add_subplot(222)
    if data['obstacles']:
        obstacles = np.array([[obs['x'], obs['y']] for obs in data['obstacles']])
        ax2.scatter(obstacles[:, 0], obstacles[:, 1], 
                   c='red', marker='s', s=10, alpha=0.6, label='障碍物')
    
    if data['path']:
        path = np.array([[p['x'], p['y']] for p in data['path']])
        ax2.plot(path[:, 0], path[:, 1], 'b-', linewidth=2, label='路径')
        
        if data['start_point']:
            start = data['start_point']
            ax2.scatter([start['x']], [start['y']], 
                       c='green', s=50, marker='o', label='起点')
        
        if data['goal_point']:
            goal = data['goal_point']
            ax2.scatter([goal['x']], [goal['y']], 
                       c='orange', s=50, marker='*', label='终点')
    
    ax2.set_xlabel('X')
    ax2.set_ylabel('Y')
    ax2.set_title('XY平面投影')
    ax2.legend()
    ax2.grid(True)
    
    # XZ平面投影
    ax3 = fig.add_subplot(223)
    if data['obstacles']:
        obstacles = np.array([[obs['x'], obs['z']] for obs in data['obstacles']])
        ax3.scatter(obstacles[:, 0], obstacles[:, 1], 
                   c='red', marker='s', s=10, alpha=0.6, label='障碍物')
    
    if data['path']:
        path = np.array([[p['x'], p['z']] for p in data['path']])
        ax3.plot(path[:, 0], path[:, 1], 'b-', linewidth=2, label='路径')
        
        if data['start_point']:
            start = data['start_point']
            ax3.scatter([start['x']], [start['z']], 
                       c='green', s=50, marker='o', label='起点')
        
        if data['goal_point']:
            goal = data['goal_point']
            ax3.scatter([goal['x']], [goal['z']], 
                       c='orange', s=50, marker='*', label='终点')
    
    ax3.set_xlabel('X')
    ax3.set_ylabel('Z')
    ax3.set_title('XZ平面投影')
    ax3.legend()
    ax3.grid(True)
    
    # YZ平面投影
    ax4 = fig.add_subplot(224)
    if data['obstacles']:
        obstacles = np.array([[obs['y'], obs['z']] for obs in data['obstacles']])
        ax4.scatter(obstacles[:, 0], obstacles[:, 1], 
                   c='red', marker='s', s=10, alpha=0.6, label='障碍物')
    
    if data['path']:
        path = np.array([[p['y'], p['z']] for p in data['path']])
        ax4.plot(path[:, 0], path[:, 1], 'b-', linewidth=2, label='路径')
        
        if data['start_point']:
            start = data['start_point']
            ax4.scatter([start['y']], [start['z']], 
                       c='green', s=50, marker='o', label='起点')
        
        if data['goal_point']:
            goal = data['goal_point']
            ax4.scatter([goal['y']], [goal['z']], 
                       c='orange', s=50, marker='*', label='终点')
    
    ax4.set_xlabel('Y')
    ax4.set_ylabel('Z')
    ax4.set_title('YZ平面投影')
    ax4.legend()
    ax4.grid(True)
    
    plt.tight_layout()
    
    # 添加统计信息
    stats_text = f"""
    地图大小: {{data['map_config']['width']}}×{{data['map_config']['height']}}×{{data['map_config']['depth']}}
    障碍物数量: {{len(data['obstacles'])}}
    规划时间: {{data['planning_time_ms']:.2f}}ms
    路径长度: {{data['path_length']:.2f}}
    路径点数: {{len(data['path']) if data['path'] else 0}}
    """
    
    fig.suptitle(f'无人机3D路径规划结果{{stats_text}}', fontsize=14)
    
    return fig

def main():
    """主函数"""
    # 加载数据
    data = load_data('{data_filename}')
    
    # 创建可视化
    fig = visualize_3d(data)
    
    # 保存图像
    plt.savefig('pathfinding_result.png', dpi=300, bbox_inches='tight')
    print("可视化图像已保存为: pathfinding_result.png")
    
    # 显示图像
    plt.show()

if __name__ == '__main__':
    main()
"#);

        let mut file = File::create(script_filename)?;
        file.write_all(script_content.as_bytes())?;
        println!("Python可视化脚本已生成: {}", script_filename);
        println!("运行命令: python {}", script_filename);
        
        Ok(())
    }
}
