use crate::types::{GridCoord, Path, MapConfig};
use crate::obstacles::{CylindricalNoFlyZone, PolygonalNoFlyZone};
use crate::pathfinding::{HierarchicalAStar, PlanningResult};
use crate::traffic::{TrafficManager, TimedPath};
use crate::optimization::{JumpPointSearch, PathSmoother};
use std::time::Instant;
use serde::{Deserialize, Serialize};

/// 路径规划请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PathPlanningRequest {
    pub start: GridCoord,
    pub goal: GridCoord,
    pub drone_id: u32,
    pub start_time: f32,
    pub max_speed: f32,
    pub safety_radius: f32,
    pub priority: u8, // 0-255，数值越高优先级越高
}

/// 路径规划响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PathPlanningResponse {
    pub success: bool,
    pub path: Option<Path>,
    pub timed_path: Option<TimedPath>,
    pub planning_time_ms: f32,
    pub error_message: Option<String>,
    pub path_quality_score: f32, // 0-100，路径质量评分
}

/// 系统统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemStats {
    pub total_requests: u64,
    pub successful_requests: u64,
    pub failed_requests: u64,
    pub average_planning_time_ms: f32,
    pub active_drones: usize,
    pub static_obstacles_count: usize,
    pub dynamic_obstacles_count: usize,
}

/// 无人机路径规划系统主类
pub struct DronePathPlanningSystem {
    config: MapConfig,
    pathfinder: HierarchicalAStar,
    traffic_manager: TrafficManager,
    jps_optimizer: JumpPointSearch,
    path_smoother: PathSmoother,
    stats: SystemStats,
}

impl DronePathPlanningSystem {
    /// 创建新的路径规划系统
    pub fn new(config: MapConfig) -> Self {
        Self {
            pathfinder: HierarchicalAStar::new(config.clone()),
            traffic_manager: TrafficManager::new(config.clone()),
            jps_optimizer: JumpPointSearch::new(config.clone()),
            path_smoother: PathSmoother::new(config.clone()),
            config,
            stats: SystemStats {
                total_requests: 0,
                successful_requests: 0,
                failed_requests: 0,
                average_planning_time_ms: 0.0,
                active_drones: 0,
                static_obstacles_count: 0,
                dynamic_obstacles_count: 0,
            },
        }
    }

    /// 设置最大规划时间
    pub fn set_max_planning_time(&mut self, time_ms: u64) {
        self.pathfinder.set_max_planning_time(time_ms);
    }

    /// 添加静态障碍物
    pub fn add_static_obstacle(&mut self, coord: GridCoord) {
        self.pathfinder.add_static_obstacle(coord);
        self.stats.static_obstacles_count += 1;
    }

    /// 批量添加静态障碍物
    pub fn add_static_obstacles_batch(&mut self, coords: &[GridCoord]) {
        self.pathfinder.add_static_obstacles_batch(coords);
        self.stats.static_obstacles_count += coords.len();
    }

    /// 添加圆柱体禁飞区
    pub fn add_cylindrical_no_fly_zone(&mut self, zone: CylindricalNoFlyZone) {
        self.pathfinder.get_dynamic_obstacles_mut().add_cylindrical_zone(zone);
        self.stats.dynamic_obstacles_count += 1;
    }

    /// 添加多边形禁飞区
    pub fn add_polygonal_no_fly_zone(&mut self, zone: PolygonalNoFlyZone) {
        self.pathfinder.get_dynamic_obstacles_mut().add_polygonal_zone(zone);
        self.stats.dynamic_obstacles_count += 1;
    }

    /// 移除禁飞区
    pub fn remove_no_fly_zone(&mut self, zone_id: u32) {
        self.pathfinder.get_dynamic_obstacles_mut().remove_zone(zone_id);
        self.stats.dynamic_obstacles_count = self.stats.dynamic_obstacles_count.saturating_sub(1);
    }

    /// 主要的路径规划函数
    pub fn plan_path(&mut self, request: PathPlanningRequest) -> PathPlanningResponse {
        let start_time = Instant::now();
        self.stats.total_requests += 1;

        // 验证请求
        if !self.config.is_valid_coord(&request.start) || !self.config.is_valid_coord(&request.goal) {
            self.stats.failed_requests += 1;
            return PathPlanningResponse {
                success: false,
                path: None,
                timed_path: None,
                planning_time_ms: start_time.elapsed().as_millis() as f32,
                error_message: Some("Invalid start or goal coordinates".to_string()),
                path_quality_score: 0.0,
            };
        }

        // 执行路径规划
        let planning_result = self.pathfinder.find_path(request.start, request.goal);
        
        match planning_result {
            PlanningResult::Success(path) => {
                // 优化路径
                let optimized_path = self.optimize_path(path);
                
                // 创建时空路径
                let timed_path = TimedPath::from_path(
                    &optimized_path,
                    request.drone_id,
                    request.start_time,
                    request.max_speed,
                    request.safety_radius,
                );

                // 检查与现有航线的冲突
                if !self.traffic_manager.can_add_path(&timed_path) {
                    self.stats.failed_requests += 1;
                    return PathPlanningResponse {
                        success: false,
                        path: Some(optimized_path),
                        timed_path: Some(timed_path),
                        planning_time_ms: start_time.elapsed().as_millis() as f32,
                        error_message: Some("Path conflicts with existing traffic".to_string()),
                        path_quality_score: 0.0,
                    };
                }

                // 添加到交通管理器
                if let Err(error) = self.traffic_manager.add_path(timed_path.clone()) {
                    self.stats.failed_requests += 1;
                    return PathPlanningResponse {
                        success: false,
                        path: Some(optimized_path),
                        timed_path: Some(timed_path),
                        planning_time_ms: start_time.elapsed().as_millis() as f32,
                        error_message: Some(error),
                        path_quality_score: 0.0,
                    };
                }

                let planning_time = start_time.elapsed().as_millis() as f32;
                let quality_score = self.calculate_path_quality(&optimized_path, &request);
                
                self.stats.successful_requests += 1;
                self.stats.active_drones = self.traffic_manager.get_active_paths().len();
                self.update_average_planning_time(planning_time);

                PathPlanningResponse {
                    success: true,
                    path: Some(optimized_path),
                    timed_path: Some(timed_path),
                    planning_time_ms: planning_time,
                    error_message: None,
                    path_quality_score: quality_score,
                }
            }
            PlanningResult::NoPathFound => {
                self.stats.failed_requests += 1;
                PathPlanningResponse {
                    success: false,
                    path: None,
                    timed_path: None,
                    planning_time_ms: start_time.elapsed().as_millis() as f32,
                    error_message: Some("No path found".to_string()),
                    path_quality_score: 0.0,
                }
            }
            PlanningResult::Timeout => {
                self.stats.failed_requests += 1;
                PathPlanningResponse {
                    success: false,
                    path: None,
                    timed_path: None,
                    planning_time_ms: start_time.elapsed().as_millis() as f32,
                    error_message: Some("Planning timeout".to_string()),
                    path_quality_score: 0.0,
                }
            }
            PlanningResult::InvalidInput => {
                self.stats.failed_requests += 1;
                PathPlanningResponse {
                    success: false,
                    path: None,
                    timed_path: None,
                    planning_time_ms: start_time.elapsed().as_millis() as f32,
                    error_message: Some("Invalid input".to_string()),
                    path_quality_score: 0.0,
                }
            }
        }
    }

    /// 优化路径
    fn optimize_path(&self, path: Path) -> Path {
        // 1. JPS优化，移除不必要的中间点
        let optimized_path = self.jps_optimizer.optimize_path(
            &path,
            self.pathfinder.get_static_obstacles(),
            self.pathfinder.get_dynamic_obstacles(),
        );

        // 2. 路径平滑
        let smoothed_path = self.path_smoother.smooth_path_spline(
            &optimized_path,
            self.pathfinder.get_static_obstacles(),
            self.pathfinder.get_dynamic_obstacles(),
            5, // 分辨率
        );

        // 3. 移除冗余点
        self.path_smoother.remove_redundant_points(
            &smoothed_path,
            self.pathfinder.get_static_obstacles(),
            self.pathfinder.get_dynamic_obstacles(),
            0.1, // 角度阈值（弧度）
        )
    }

    /// 计算路径质量评分
    fn calculate_path_quality(&self, path: &Path, request: &PathPlanningRequest) -> f32 {
        if path.waypoints.is_empty() {
            return 0.0;
        }

        let mut score = 100.0;

        // 距离效率（与直线距离的比值）
        let direct_distance = request.start.euclidean_distance(&request.goal);
        let path_distance = path.calculate_distance();
        let distance_efficiency = direct_distance / path_distance.max(0.1);
        score *= distance_efficiency;

        // 平滑度（转向次数的倒数）
        let mut turn_count = 0;
        for i in 1..path.waypoints.len() - 1 {
            let prev_dir = (
                path.waypoints[i].x - path.waypoints[i-1].x,
                path.waypoints[i].y - path.waypoints[i-1].y,
                path.waypoints[i].z - path.waypoints[i-1].z,
            );
            let next_dir = (
                path.waypoints[i+1].x - path.waypoints[i].x,
                path.waypoints[i+1].y - path.waypoints[i].y,
                path.waypoints[i+1].z - path.waypoints[i].z,
            );
            
            if prev_dir != next_dir {
                turn_count += 1;
            }
        }
        
        let smoothness = 1.0 / (1.0 + turn_count as f32 / path.waypoints.len() as f32);
        score *= smoothness;

        // 规划时间惩罚
        if path.planning_time_ms > 5.0 {
            score *= 0.8;
        }

        score.min(100.0).max(0.0)
    }

    /// 更新平均规划时间
    fn update_average_planning_time(&mut self, new_time: f32) {
        let total_requests = self.stats.total_requests as f32;
        self.stats.average_planning_time_ms = 
            (self.stats.average_planning_time_ms * (total_requests - 1.0) + new_time) / total_requests;
    }

    /// 移除无人机航线
    pub fn remove_drone_path(&mut self, drone_id: u32) -> bool {
        if self.traffic_manager.remove_path(drone_id).is_some() {
            self.stats.active_drones = self.traffic_manager.get_active_paths().len();
            true
        } else {
            false
        }
    }

    /// 清理过期航线
    pub fn cleanup_expired_paths(&mut self, current_time: f32) {
        self.traffic_manager.cleanup_expired_paths(current_time);
        self.stats.active_drones = self.traffic_manager.get_active_paths().len();
    }

    /// 获取系统统计信息
    pub fn get_stats(&self) -> &SystemStats {
        &self.stats
    }

    /// 获取交通统计信息
    pub fn get_traffic_stats(&self) -> (usize, f32, f32) {
        self.traffic_manager.get_stats()
    }

    /// 重置统计信息
    pub fn reset_stats(&mut self) {
        self.stats = SystemStats {
            total_requests: 0,
            successful_requests: 0,
            failed_requests: 0,
            average_planning_time_ms: 0.0,
            active_drones: self.traffic_manager.get_active_paths().len(),
            static_obstacles_count: self.stats.static_obstacles_count,
            dynamic_obstacles_count: self.stats.dynamic_obstacles_count,
        };
    }

    /// 获取地图配置
    pub fn get_config(&self) -> &MapConfig {
        &self.config
    }
}
