mod types;
mod obstacles;
mod spatial_index;
mod pathfinding;
mod traffic;
mod optimization;
mod system;

use types::{GridCoord, MapConfig, Point3D};
use obstacles::{CylindricalNoFlyZone, PolygonalNoFlyZone};
use system::{DronePathPlanningSystem, PathPlanningRequest};
use std::time::Instant;
use rand::Rng;

fn main() {
    println!("无人机三维路径规划系统启动中...");

    // 创建地图配置
    let config = MapConfig::default();
    println!("地图大小: {}x{}x{}", config.width, config.height, config.depth);

    // 创建路径规划系统
    let mut system = DronePathPlanningSystem::new(config.clone());
    system.set_max_planning_time(10); // 10毫秒超时

    println!("正在生成测试环境...");

    // 生成随机障碍物（模拟建筑物和山体）
    generate_random_obstacles(&mut system, 50000);

    // 添加一些禁飞区
    add_test_no_fly_zones(&mut system);

    println!("开始性能测试...");

    // 运行性能测试
    run_performance_test(&mut system, 1000);

    // 显示统计信息
    display_system_stats(&system);
}

/// 生成随机障碍物
fn generate_random_obstacles(system: &mut DronePathPlanningSystem, count: usize) {
    let mut rng = rand::thread_rng();
    let config = system.get_config();
    let mut obstacles = Vec::new();

    for _ in 0..count {
        let x = rng.gen_range(0..config.width);
        let y = rng.gen_range(0..config.height);
        let z = rng.gen_range(0..config.depth);
        obstacles.push(GridCoord::new(x, y, z));
    }

    let start_time = Instant::now();
    system.add_static_obstacles_batch(&obstacles);
    let elapsed = start_time.elapsed();

    println!("添加 {} 个障碍物耗时: {:.2}ms", count, elapsed.as_millis());
}

/// 添加测试禁飞区
fn add_test_no_fly_zones(system: &mut DronePathPlanningSystem) {
    // 添加圆柱体禁飞区（模拟机场）
    let airport_zone = CylindricalNoFlyZone::new(
        Point3D::new(5000.0, 5000.0, 0.0),
        500.0, // 半径500米
        0.0,   // 最低高度
        200.0, // 最高高度
        1,     // ID
    );
    system.add_cylindrical_no_fly_zone(airport_zone);

    // 添加多边形禁飞区（模拟军事基地）
    let military_vertices = vec![
        Point3D::new(2000.0, 2000.0, 0.0),
        Point3D::new(3000.0, 2000.0, 0.0),
        Point3D::new(3000.0, 3000.0, 0.0),
        Point3D::new(2000.0, 3000.0, 0.0),
    ];
    let military_zone = PolygonalNoFlyZone::new(
        military_vertices,
        0.0,   // 最低高度
        500.0, // 最高高度
        2,     // ID
    );
    system.add_polygonal_no_fly_zone(military_zone);

    println!("添加了 2 个禁飞区");
}

/// 运行性能测试
fn run_performance_test(system: &mut DronePathPlanningSystem, test_count: usize) {
    let mut rng = rand::thread_rng();
    let config = system.get_config();

    let mut total_time = 0.0;
    let mut successful_plans = 0;
    let mut planning_times = Vec::new();

    println!("执行 {} 次路径规划测试...", test_count);

    for i in 0..test_count {
        // 生成随机起点和终点
        let start = GridCoord::new(
            rng.gen_range(0..config.width),
            rng.gen_range(0..config.height),
            rng.gen_range(0..config.depth),
        );
        let goal = GridCoord::new(
            rng.gen_range(0..config.width),
            rng.gen_range(0..config.height),
            rng.gen_range(0..config.depth),
        );

        let request = PathPlanningRequest {
            start,
            goal,
            drone_id: i as u32,
            start_time: 0.0,
            max_speed: 10.0, // 10 m/s
            safety_radius: 5.0, // 5米安全半径
            priority: 128,
        };

        let start_time = Instant::now();
        let response = system.plan_path(request);
        let elapsed = start_time.elapsed().as_millis() as f32;

        total_time += elapsed;
        planning_times.push(elapsed);

        if response.success {
            successful_plans += 1;
        }

        // 每100次测试显示进度
        if (i + 1) % 100 == 0 {
            println!("完成 {}/{} 次测试", i + 1, test_count);
        }

        // 移除路径以避免过多的交通冲突
        if response.success {
            system.remove_drone_path(i as u32);
        }
    }

    // 计算统计信息
    let average_time = total_time / test_count as f32;
    planning_times.sort_by(|a, b| a.partial_cmp(b).unwrap());
    let median_time = planning_times[test_count / 2];
    let p95_time = planning_times[(test_count as f32 * 0.95) as usize];
    let p99_time = planning_times[(test_count as f32 * 0.99) as usize];
    let max_time = planning_times[test_count - 1];

    println!("\n=== 性能测试结果 ===");
    println!("总测试次数: {}", test_count);
    println!("成功规划: {} ({:.1}%)", successful_plans, successful_plans as f32 / test_count as f32 * 100.0);
    println!("平均规划时间: {:.2}ms", average_time);
    println!("中位数规划时间: {:.2}ms", median_time);
    println!("95%分位数: {:.2}ms", p95_time);
    println!("99%分位数: {:.2}ms", p99_time);
    println!("最大规划时间: {:.2}ms", max_time);

    // 检查毫秒级性能目标
    let millisecond_success_rate = planning_times.iter()
        .filter(|&&time| time <= 1.0)
        .count() as f32 / test_count as f32 * 100.0;

    println!("毫秒级规划成功率: {:.1}%", millisecond_success_rate);

    if average_time <= 5.0 && p95_time <= 10.0 {
        println!("✅ 性能目标达成！");
    } else {
        println!("❌ 性能目标未达成，需要进一步优化");
    }
}

/// 显示系统统计信息
fn display_system_stats(system: &DronePathPlanningSystem) {
    let stats = system.get_stats();
    let (active_drones, avg_path_length, avg_duration) = system.get_traffic_stats();

    println!("\n=== 系统统计信息 ===");
    println!("总请求数: {}", stats.total_requests);
    println!("成功请求数: {}", stats.successful_requests);
    println!("失败请求数: {}", stats.failed_requests);
    println!("平均规划时间: {:.2}ms", stats.average_planning_time_ms);
    println!("活跃无人机数: {}", stats.active_drones);
    println!("静态障碍物数: {}", stats.static_obstacles_count);
    println!("动态障碍物数: {}", stats.dynamic_obstacles_count);
    println!("平均路径长度: {:.1} 个路径点", avg_path_length);
    println!("平均飞行时长: {:.1} 秒", avg_duration);
}
