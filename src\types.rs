use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::hash::{<PERSON><PERSON>, <PERSON><PERSON>};

/// 三维坐标点
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub struct Point3D {
    pub x: f32,
    pub y: f32,
    pub z: f32,
}

impl Point3D {
    pub fn new(x: f32, y: f32, z: f32) -> Self {
        Self { x, y, z }
    }
}

/// 三维向量
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub struct Vector3D {
    pub x: f32,
    pub y: f32,
    pub z: f32,
}

impl Vector3D {
    pub fn new(x: f32, y: f32, z: f32) -> Self {
        Self { x, y, z }
    }

    pub fn angle(&self, other: &Vector3D) -> f32 {
        let dot = self.x * other.x + self.y * other.y + self.z * other.z;
        let mag1 = (self.x * self.x + self.y * self.y + self.z * self.z).sqrt();
        let mag2 = (other.x * other.x + other.y * other.y + other.z * other.z).sqrt();

        if mag1 == 0.0 || mag2 == 0.0 {
            0.0
        } else {
            (dot / (mag1 * mag2)).acos()
        }
    }
}

/// 网格坐标 - 整数坐标系
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Ord, PartialOrd, Serialize, Deserialize)]
pub struct GridCoord {
    pub x: i32,
    pub y: i32,
    pub z: i32,
}

impl GridCoord {
    pub fn new(x: i32, y: i32, z: i32) -> Self {
        Self { x, y, z }
    }

    /// 转换为世界坐标
    pub fn to_world(&self, grid_size: f32) -> Point3D {
        Point3D::new(
            self.x as f32 * grid_size,
            self.y as f32 * grid_size,
            self.z as f32 * grid_size,
        )
    }

    /// 从世界坐标转换
    pub fn from_world(point: &Point3D, grid_size: f32) -> Self {
        Self {
            x: (point.x / grid_size).floor() as i32,
            y: (point.y / grid_size).floor() as i32,
            z: (point.z / grid_size).floor() as i32,
        }
    }

    /// 计算曼哈顿距离
    pub fn manhattan_distance(&self, other: &GridCoord) -> i32 {
        (self.x - other.x).abs() + (self.y - other.y).abs() + (self.z - other.z).abs()
    }

    /// 计算欧几里得距离
    pub fn euclidean_distance(&self, other: &GridCoord) -> f32 {
        let dx = (self.x - other.x) as f32;
        let dy = (self.y - other.y) as f32;
        let dz = (self.z - other.z) as f32;
        (dx * dx + dy * dy + dz * dz).sqrt()
    }

    /// 获取26个相邻网格坐标
    pub fn neighbors(&self) -> Vec<GridCoord> {
        let mut neighbors = Vec::with_capacity(26);
        for dx in -1..=1 {
            for dy in -1..=1 {
                for dz in -1..=1 {
                    if dx == 0 && dy == 0 && dz == 0 {
                        continue;
                    }
                    neighbors.push(GridCoord::new(
                        self.x + dx,
                        self.y + dy,
                        self.z + dz,
                    ));
                }
            }
        }
        neighbors
    }

    /// 获取6个面相邻的网格坐标（不包括对角线）
    pub fn face_neighbors(&self) -> Vec<GridCoord> {
        vec![
            GridCoord::new(self.x + 1, self.y, self.z),
            GridCoord::new(self.x - 1, self.y, self.z),
            GridCoord::new(self.x, self.y + 1, self.z),
            GridCoord::new(self.x, self.y - 1, self.z),
            GridCoord::new(self.x, self.y, self.z + 1),
            GridCoord::new(self.x, self.y, self.z - 1),
        ]
    }
}

/// 边界框
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BoundingBox {
    pub min: GridCoord,
    pub max: GridCoord,
}

impl BoundingBox {
    pub fn new(min: GridCoord, max: GridCoord) -> Self {
        Self { min, max }
    }

    /// 检查点是否在边界框内
    pub fn contains(&self, point: &GridCoord) -> bool {
        point.x >= self.min.x && point.x <= self.max.x &&
        point.y >= self.min.y && point.y <= self.max.y &&
        point.z >= self.min.z && point.z <= self.max.z
    }

    /// 检查两个边界框是否相交
    pub fn intersects(&self, other: &BoundingBox) -> bool {
        self.min.x <= other.max.x && self.max.x >= other.min.x &&
        self.min.y <= other.max.y && self.max.y >= other.min.y &&
        self.min.z <= other.max.z && self.max.z >= other.min.z
    }

    /// 计算体积
    pub fn volume(&self) -> i64 {
        let dx = (self.max.x - self.min.x + 1) as i64;
        let dy = (self.max.y - self.min.y + 1) as i64;
        let dz = (self.max.z - self.min.z + 1) as i64;
        dx * dy * dz
    }
}

/// 路径节点
#[derive(Debug, Clone)]
pub struct PathNode {
    pub coord: GridCoord,
    pub g_cost: f32,  // 从起点到当前节点的实际代价
    pub h_cost: f32,  // 从当前节点到终点的启发式代价
    pub parent: Option<GridCoord>,
    pub timestamp: f32, // 时间戳，用于4D路径规划
}

impl PathNode {
    pub fn new(coord: GridCoord, g_cost: f32, h_cost: f32, parent: Option<GridCoord>) -> Self {
        Self {
            coord,
            g_cost,
            h_cost,
            parent,
            timestamp: 0.0,
        }
    }

    pub fn f_cost(&self) -> f32 {
        self.g_cost + self.h_cost
    }
}

impl PartialEq for PathNode {
    fn eq(&self, other: &Self) -> bool {
        self.coord == other.coord
    }
}

impl Eq for PathNode {}

impl Hash for PathNode {
    fn hash<H: Hasher>(&self, state: &mut H) {
        self.coord.hash(state);
    }
}

/// 路径表示
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Path {
    pub waypoints: Vec<GridCoord>,
    pub total_cost: f32,
    pub planning_time_ms: f32,
}

impl Path {
    pub fn new() -> Self {
        Self {
            waypoints: Vec::new(),
            total_cost: 0.0,
            planning_time_ms: 0.0,
        }
    }

    pub fn length(&self) -> usize {
        self.waypoints.len()
    }

    pub fn is_empty(&self) -> bool {
        self.waypoints.is_empty()
    }

    /// 计算路径的总长度
    pub fn calculate_distance(&self) -> f32 {
        if self.waypoints.len() < 2 {
            return 0.0;
        }

        let mut total_distance = 0.0;
        for i in 1..self.waypoints.len() {
            total_distance += self.waypoints[i-1].euclidean_distance(&self.waypoints[i]);
        }
        total_distance
    }
}

/// 地图配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MapConfig {
    pub width: i32,
    pub height: i32,
    pub depth: i32,
    pub grid_size: f32,
    pub coarse_grid_factor: i32, // 粗粒度网格的缩放因子
}

impl Default for MapConfig {
    fn default() -> Self {
        Self {
            width: 10000,
            height: 10000,
            depth: 1000,
            grid_size: 1.0,
            coarse_grid_factor: 10, // 10x10x10的粗粒度网格
        }
    }
}

impl MapConfig {
    /// 检查坐标是否在地图范围内
    pub fn is_valid_coord(&self, coord: &GridCoord) -> bool {
        coord.x >= 0 && coord.x < self.width &&
        coord.y >= 0 && coord.y < self.height &&
        coord.z >= 0 && coord.z < self.depth
    }

    /// 转换为粗粒度坐标
    pub fn to_coarse_coord(&self, coord: &GridCoord) -> GridCoord {
        GridCoord::new(
            coord.x / self.coarse_grid_factor,
            coord.y / self.coarse_grid_factor,
            coord.z / self.coarse_grid_factor,
        )
    }

    /// 从粗粒度坐标转换回细粒度坐标范围
    pub fn from_coarse_coord(&self, coarse_coord: &GridCoord) -> BoundingBox {
        let min = GridCoord::new(
            coarse_coord.x * self.coarse_grid_factor,
            coarse_coord.y * self.coarse_grid_factor,
            coarse_coord.z * self.coarse_grid_factor,
        );
        let max = GridCoord::new(
            (coarse_coord.x + 1) * self.coarse_grid_factor - 1,
            (coarse_coord.y + 1) * self.coarse_grid_factor - 1,
            (coarse_coord.z + 1) * self.coarse_grid_factor - 1,
        );
        BoundingBox::new(min, max)
    }
}
