use crate::types::{GridCoord, Path, MapConfig};
use crate::adaptive_grid::{AdaptiveGridMap, AdaptiveNode};
use crate::obstacles::{StaticObstacleMap, DynamicObstacleManager};
use std::collections::{HashMap, HashSet, BinaryHeap};
use std::cmp::Ordering;
use std::time::Instant;

/// 自适应路径节点
#[derive(Debu<PERSON>, Clone)]
struct AdaptivePathNode {
    node_center: GridCoord,
    g_cost: f32,
    h_cost: f32,
    parent: Option<GridCoord>,
}

impl AdaptivePathNode {
    fn f_cost(&self) -> f32 {
        self.g_cost + self.h_cost
    }
}

impl PartialEq for AdaptivePathNode {
    fn eq(&self, other: &Self) -> bool {
        self.node_center == other.node_center
    }
}

impl Eq for AdaptivePathNode {}

impl PartialOrd for AdaptivePathNode {
    fn partial_cmp(&self, other: &Self) -> Option<Ordering> {
        // 反转比较以实现最小堆
        other.f_cost().partial_cmp(&self.f_cost())
    }
}

impl Ord for AdaptivePathNode {
    fn cmp(&self, other: &Self) -> Ordering {
        self.partial_cmp(other).unwrap_or(Ordering::Equal)
    }
}

/// 自适应路径规划器
pub struct AdaptivePathfinder {
    grid_map: AdaptiveGridMap,
    config: MapConfig,
    dynamic_obstacles: DynamicObstacleManager,
}

impl AdaptivePathfinder {
    pub fn new(config: MapConfig, static_obstacles: &StaticObstacleMap) -> Self {
        let grid_map = AdaptiveGridMap::new(config.clone(), static_obstacles);
        let dynamic_obstacles = DynamicObstacleManager::new(config.clone());
        
        Self {
            grid_map,
            config,
            dynamic_obstacles,
        }
    }

    /// 统一的自适应路径规划
    pub fn find_path(&self, start: GridCoord, goal: GridCoord) -> Option<Path> {
        let start_time = Instant::now();
        
        // 找到起点和终点所在的叶子节点
        let start_node = self.grid_map.find_node_at(&start)?;
        let goal_node = self.grid_map.find_node_at(&goal)?;
        
        if start_node.is_obstacle || goal_node.is_obstacle {
            return None;
        }

        // 使用A*在自适应网格上搜索
        let node_path = self.adaptive_astar(start_node, goal_node, start_time)?;
        
        // 将节点路径转换为网格路径
        let grid_path = self.convert_to_grid_path(&node_path, start, goal);
        
        let mut path = Path::new();
        path.waypoints = grid_path;
        path.total_cost = path.calculate_distance();
        path.planning_time_ms = start_time.elapsed().as_millis() as f32;
        
        Some(path)
    }

    /// 自适应A*算法
    fn adaptive_astar(&self, start_node: &AdaptiveNode, goal_node: &AdaptiveNode, start_time: Instant) -> Option<Vec<GridCoord>> {
        let mut open_set = BinaryHeap::new();
        let mut came_from: HashMap<GridCoord, GridCoord> = HashMap::new();
        let mut g_score: HashMap<GridCoord, f32> = HashMap::new();
        let mut closed_set: HashSet<GridCoord> = HashSet::new();
        
        let start_center = start_node.center;
        let goal_center = goal_node.center;
        
        g_score.insert(start_center, 0.0);
        let h_cost = self.adaptive_heuristic(&start_center, &goal_center);
        
        open_set.push(AdaptivePathNode {
            node_center: start_center,
            g_cost: 0.0,
            h_cost,
            parent: None,
        });

        while let Some(current_node) = open_set.pop() {
            // 时间限制检查
            // if start_time.elapsed().as_millis() > 5 {
            //     break;
            // }

            let current_center = current_node.node_center;

            if current_center == goal_center {
                // 重构路径
                let mut path = vec![current_center];
                let mut current = current_center;
                while let Some(&parent) = came_from.get(&current) {
                    path.push(parent);
                    current = parent;
                }
                path.reverse();
                return Some(path);
            }

            closed_set.insert(current_center);

            // 获取邻居节点索引
            let neighbor_indices = self.grid_map.get_node_neighbor_indices(&current_center);

            for neighbor_idx in neighbor_indices {
                if let Some(neighbor) = self.grid_map.get_node_by_index(neighbor_idx) {
                    let neighbor_center = neighbor.center;

                    if closed_set.contains(&neighbor_center) || neighbor.is_obstacle {
                        continue;
                    }

                    // 检查动态障碍物
                    if self.dynamic_obstacles.is_in_no_fly_zone(&neighbor_center) {
                        continue;
                    }

                    // 获取当前节点用于计算移动代价
                    if let Some(current_node) = self.grid_map.find_node_at(&current_center) {
                        // 计算移动代价，考虑不同分辨率的节点
                        let move_cost = self.calculate_move_cost(current_node, neighbor);
                        let tentative_g_score = g_score.get(&current_center).unwrap_or(&f32::INFINITY) + move_cost;

                        if tentative_g_score < *g_score.get(&neighbor_center).unwrap_or(&f32::INFINITY) {
                            came_from.insert(neighbor_center, current_center);
                            g_score.insert(neighbor_center, tentative_g_score);

                            let h_cost = self.adaptive_heuristic(&neighbor_center, &goal_center);

                            open_set.push(AdaptivePathNode {
                                node_center: neighbor_center,
                                g_cost: tentative_g_score,
                                h_cost,
                                parent: Some(current_center),
                            });
                        }
                    }
                }
            }
        }

        None
    }

    /// 自适应启发式函数
    fn adaptive_heuristic(&self, current: &GridCoord, goal: &GridCoord) -> f32 {
        // 使用欧几里得距离，但考虑节点级别的影响
        let base_distance = current.euclidean_distance(goal);
        
        // 获取当前节点的级别信息
        if let Some(current_node) = self.grid_map.find_node_at(current) {
            // 高级别（细粒度）节点的启发式值稍微降低，鼓励使用
            let level_factor = 1.0 - (current_node.level as f32 * 0.1);
            base_distance * level_factor
        } else {
            base_distance
        }
    }

    /// 计算移动代价
    fn calculate_move_cost(&self, from: &AdaptiveNode, to: &AdaptiveNode) -> f32 {
        let base_distance = from.center.euclidean_distance(&to.center);
        
        // 考虑节点的通行代价倍数
        let cost_multiplier = (from.cost_multiplier + to.cost_multiplier) / 2.0;
        
        // 考虑分辨率差异的惩罚
        let level_diff = (from.level as i32 - to.level as i32).abs() as f32;
        let level_penalty = 1.0 + level_diff * 0.1;
        
        base_distance * cost_multiplier * level_penalty
    }

    /// 将节点路径转换为网格路径
    fn convert_to_grid_path(&self, node_path: &[GridCoord], start: GridCoord, goal: GridCoord) -> Vec<GridCoord> {
        if node_path.is_empty() {
            return vec![];
        }

        let mut grid_path = vec![start];
        
        for i in 1..node_path.len() {
            let from_center = node_path[i-1];
            let to_center = node_path[i];
            
            // 在两个节点中心之间插入中间点
            let intermediate_points = self.interpolate_between_centers(&from_center, &to_center);
            grid_path.extend(intermediate_points);
        }
        
        // 确保终点是目标点
        if grid_path.last() != Some(&goal) {
            grid_path.push(goal);
        }
        
        grid_path
    }

    /// 在两个节点中心之间插值
    fn interpolate_between_centers(&self, from: &GridCoord, to: &GridCoord) -> Vec<GridCoord> {
        let mut points = Vec::new();
        
        let dx = to.x - from.x;
        let dy = to.y - from.y;
        let dz = to.z - from.z;
        
        let steps = dx.abs().max(dy.abs()).max(dz.abs());
        
        if steps <= 1 {
            return vec![*to];
        }
        
        for i in 1..=steps {
            let t = i as f32 / steps as f32;
            let point = GridCoord::new(
                from.x + (dx as f32 * t) as i32,
                from.y + (dy as f32 * t) as i32,
                from.z + (dz as f32 * t) as i32,
            );
            points.push(point);
        }
        
        points
    }

    /// 获取动态障碍物管理器的可变引用
    pub fn get_dynamic_obstacles_mut(&mut self) -> &mut DynamicObstacleManager {
        &mut self.dynamic_obstacles
    }

    /// 获取网格统计信息
    pub fn get_grid_stats(&self) -> (usize, f32) {
        self.grid_map.get_stats()
    }

    /// 添加静态障碍物（需要重建网格）
    pub fn rebuild_with_obstacles(&mut self, static_obstacles: &StaticObstacleMap) {
        self.grid_map = AdaptiveGridMap::new(self.config.clone(), static_obstacles);
    }
}
