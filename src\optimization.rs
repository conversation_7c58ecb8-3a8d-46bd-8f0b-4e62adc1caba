use crate::types::{<PERSON>ridCoord, Path, MapConfig, Vector3D};
use crate::obstacles::{StaticObstacleMap, DynamicObstacleManager};

/// Jump Point Search优化器
pub struct JumpPointSearch {
    config: MapConfig,
}

impl JumpPointSearch {
    pub fn new(config: MapConfig) -> Self {
        Self { config }
    }

    /// 检查是否可以直线到达目标
    pub fn has_line_of_sight(
        &self,
        start: &GridCoord,
        end: &GridCoord,
        static_obstacles: &StaticObstacleMap,
        dynamic_obstacles: &DynamicObstacleManager,
    ) -> bool {
        let dx = end.x - start.x;
        let dy = end.y - start.y;
        let dz = end.z - start.z;
        
        let steps = dx.abs().max(dy.abs()).max(dz.abs());
        if steps == 0 {
            return true;
        }

        let step_x = dx as f32 / steps as f32;
        let step_y = dy as f32 / steps as f32;
        let step_z = dz as f32 / steps as f32;

        for i in 1..steps {
            let check_coord = GridCoord::new(
                start.x + (step_x * i as f32).round() as i32,
                start.y + (step_y * i as f32).round() as i32,
                start.z + (step_z * i as f32).round() as i32,
            );

            if !self.config.is_valid_coord(&check_coord) ||
               static_obstacles.is_obstacle(&check_coord) ||
               dynamic_obstacles.is_in_no_fly_zone(&check_coord) {
                return false;
            }
        }

        true
    }

    /// 寻找跳跃点
    pub fn find_jump_point(
        &self,
        current: &GridCoord,
        direction: &GridCoord,
        goal: &GridCoord,
        static_obstacles: &StaticObstacleMap,
        dynamic_obstacles: &DynamicObstacleManager,
    ) -> Option<GridCoord> {
        let next = GridCoord::new(
            current.x + direction.x,
            current.y + direction.y,
            current.z + direction.z,
        );

        if !self.config.is_valid_coord(&next) ||
           static_obstacles.is_obstacle(&next) ||
           dynamic_obstacles.is_in_no_fly_zone(&next) {
            return None;
        }

        if next == *goal {
            return Some(next);
        }

        // 检查是否为跳跃点
        if self.is_jump_point(&next, direction, static_obstacles, dynamic_obstacles) {
            return Some(next);
        }

        // 递归搜索
        self.find_jump_point(&next, direction, goal, static_obstacles, dynamic_obstacles)
    }

    /// 检查是否为跳跃点
    fn is_jump_point(
        &self,
        coord: &GridCoord,
        direction: &GridCoord,
        static_obstacles: &StaticObstacleMap,
        dynamic_obstacles: &DynamicObstacleManager,
    ) -> bool {
        // 简化的跳跃点检测逻辑
        // 检查是否有强制邻居
        let perpendicular_dirs = self.get_perpendicular_directions(direction);
        
        for perp_dir in perpendicular_dirs {
            let neighbor = GridCoord::new(
                coord.x + perp_dir.x,
                coord.y + perp_dir.y,
                coord.z + perp_dir.z,
            );
            
            let behind_neighbor = GridCoord::new(
                coord.x - direction.x + perp_dir.x,
                coord.y - direction.y + perp_dir.y,
                coord.z - direction.z + perp_dir.z,
            );

            if self.config.is_valid_coord(&neighbor) &&
               !static_obstacles.is_obstacle(&neighbor) &&
               !dynamic_obstacles.is_in_no_fly_zone(&neighbor) &&
               (static_obstacles.is_obstacle(&behind_neighbor) ||
                dynamic_obstacles.is_in_no_fly_zone(&behind_neighbor)) {
                return true;
            }
        }

        false
    }

    /// 获取垂直方向
    fn get_perpendicular_directions(&self, direction: &GridCoord) -> Vec<GridCoord> {
        let mut perpendicular = Vec::new();
        
        // 简化：只考虑主要的垂直方向
        if direction.x != 0 {
            perpendicular.push(GridCoord::new(0, 1, 0));
            perpendicular.push(GridCoord::new(0, -1, 0));
            perpendicular.push(GridCoord::new(0, 0, 1));
            perpendicular.push(GridCoord::new(0, 0, -1));
        }
        if direction.y != 0 {
            perpendicular.push(GridCoord::new(1, 0, 0));
            perpendicular.push(GridCoord::new(-1, 0, 0));
            perpendicular.push(GridCoord::new(0, 0, 1));
            perpendicular.push(GridCoord::new(0, 0, -1));
        }
        if direction.z != 0 {
            perpendicular.push(GridCoord::new(1, 0, 0));
            perpendicular.push(GridCoord::new(-1, 0, 0));
            perpendicular.push(GridCoord::new(0, 1, 0));
            perpendicular.push(GridCoord::new(0, -1, 0));
        }

        perpendicular
    }

    /// 优化路径，移除不必要的中间点
    pub fn optimize_path(
        &self,
        path: &Path,
        static_obstacles: &StaticObstacleMap,
        dynamic_obstacles: &DynamicObstacleManager,
    ) -> Path {
        if path.waypoints.len() <= 2 {
            return path.clone();
        }

        let mut optimized_waypoints = vec![path.waypoints[0]];
        let mut current_index = 0;

        while current_index < path.waypoints.len() - 1 {
            let mut farthest_reachable = current_index + 1;

            // 找到最远的可直达点
            for i in (current_index + 2)..path.waypoints.len() {
                if self.has_line_of_sight(
                    &path.waypoints[current_index],
                    &path.waypoints[i],
                    static_obstacles,
                    dynamic_obstacles,
                ) {
                    farthest_reachable = i;
                } else {
                    break;
                }
            }

            optimized_waypoints.push(path.waypoints[farthest_reachable]);
            current_index = farthest_reachable;
        }

        let mut optimized_path = Path::new();
        optimized_path.waypoints = optimized_waypoints;
        optimized_path.total_cost = optimized_path.calculate_distance();
        optimized_path.planning_time_ms = path.planning_time_ms;

        optimized_path
    }
}

/// 路径平滑器
pub struct PathSmoother {
    config: MapConfig,
}

impl PathSmoother {
    pub fn new(config: MapConfig) -> Self {
        Self { config }
    }

    /// 使用贝塞尔曲线平滑路径
    pub fn smooth_path_bezier(
        &self,
        path: &Path,
        static_obstacles: &StaticObstacleMap,
        dynamic_obstacles: &DynamicObstacleManager,
        smoothing_factor: f32,
    ) -> Path {
        if path.waypoints.len() <= 2 {
            return path.clone();
        }

        let mut smoothed_waypoints = Vec::new();
        smoothed_waypoints.push(path.waypoints[0]);

        for i in 1..path.waypoints.len() - 1 {
            let prev = &path.waypoints[i - 1];
            let current = &path.waypoints[i];
            let next = &path.waypoints[i + 1];

            // 计算控制点
            let control1 = self.interpolate_point(prev, current, smoothing_factor);
            let control2 = self.interpolate_point(next, current, smoothing_factor);

            // 生成贝塞尔曲线上的点
            let bezier_points = self.generate_bezier_points(
                &path.waypoints[i - 1],
                &control1,
                &control2,
                current,
                10, // 每段生成10个点
            );

            // 验证生成的点是否安全
            for point in bezier_points {
                if self.config.is_valid_coord(&point) &&
                   !static_obstacles.is_obstacle(&point) &&
                   !dynamic_obstacles.is_in_no_fly_zone(&point) {
                    smoothed_waypoints.push(point);
                }
            }
        }

        smoothed_waypoints.push(path.waypoints[path.waypoints.len() - 1]);

        let mut smoothed_path = Path::new();
        smoothed_path.waypoints = smoothed_waypoints;
        smoothed_path.total_cost = smoothed_path.calculate_distance();
        smoothed_path.planning_time_ms = path.planning_time_ms;

        smoothed_path
    }

    /// 线性插值
    fn interpolate_point(&self, p1: &GridCoord, p2: &GridCoord, factor: f32) -> GridCoord {
        GridCoord::new(
            (p1.x as f32 + (p2.x - p1.x) as f32 * factor) as i32,
            (p1.y as f32 + (p2.y - p1.y) as f32 * factor) as i32,
            (p1.z as f32 + (p2.z - p1.z) as f32 * factor) as i32,
        )
    }

    /// 生成三次贝塞尔曲线上的点
    fn generate_bezier_points(
        &self,
        p0: &GridCoord,
        p1: &GridCoord,
        p2: &GridCoord,
        p3: &GridCoord,
        num_points: usize,
    ) -> Vec<GridCoord> {
        let mut points = Vec::new();

        for i in 1..num_points {
            let t = i as f32 / num_points as f32;
            let t2 = t * t;
            let t3 = t2 * t;
            let mt = 1.0 - t;
            let mt2 = mt * mt;
            let mt3 = mt2 * mt;

            let x = mt3 * p0.x as f32 + 
                   3.0 * mt2 * t * p1.x as f32 + 
                   3.0 * mt * t2 * p2.x as f32 + 
                   t3 * p3.x as f32;
            let y = mt3 * p0.y as f32 + 
                   3.0 * mt2 * t * p1.y as f32 + 
                   3.0 * mt * t2 * p2.y as f32 + 
                   t3 * p3.y as f32;
            let z = mt3 * p0.z as f32 + 
                   3.0 * mt2 * t * p1.z as f32 + 
                   3.0 * mt * t2 * p2.z as f32 + 
                   t3 * p3.z as f32;

            points.push(GridCoord::new(x.round() as i32, y.round() as i32, z.round() as i32));
        }

        points
    }

    /// 使用样条插值平滑路径
    pub fn smooth_path_spline(
        &self,
        path: &Path,
        static_obstacles: &StaticObstacleMap,
        dynamic_obstacles: &DynamicObstacleManager,
        resolution: usize,
    ) -> Path {
        if path.waypoints.len() <= 2 {
            return path.clone();
        }

        let mut smoothed_waypoints = Vec::new();
        
        // 为每对相邻点生成插值点
        for i in 0..path.waypoints.len() - 1 {
            let start = &path.waypoints[i];
            let end = &path.waypoints[i + 1];
            
            smoothed_waypoints.push(*start);
            
            // 生成中间插值点
            for j in 1..resolution {
                let t = j as f32 / resolution as f32;
                let interpolated = GridCoord::new(
                    (start.x as f32 + (end.x - start.x) as f32 * t) as i32,
                    (start.y as f32 + (end.y - start.y) as f32 * t) as i32,
                    (start.z as f32 + (end.z - start.z) as f32 * t) as i32,
                );
                
                // 检查插值点是否安全
                if self.config.is_valid_coord(&interpolated) &&
                   !static_obstacles.is_obstacle(&interpolated) &&
                   !dynamic_obstacles.is_in_no_fly_zone(&interpolated) {
                    smoothed_waypoints.push(interpolated);
                }
            }
        }
        
        smoothed_waypoints.push(path.waypoints[path.waypoints.len() - 1]);

        let mut smoothed_path = Path::new();
        smoothed_path.waypoints = smoothed_waypoints;
        smoothed_path.total_cost = smoothed_path.calculate_distance();
        smoothed_path.planning_time_ms = path.planning_time_ms;

        smoothed_path
    }

    /// 移除冗余点
    pub fn remove_redundant_points(
        &self,
        path: &Path,
        _static_obstacles: &StaticObstacleMap,
        _dynamic_obstacles: &DynamicObstacleManager,
        angle_threshold: f32,
    ) -> Path {
        if path.waypoints.len() <= 2 {
            return path.clone();
        }

        let mut filtered_waypoints = vec![path.waypoints[0]];

        for i in 1..path.waypoints.len() - 1 {
            let prev = &path.waypoints[i - 1];
            let current = &path.waypoints[i];
            let next = &path.waypoints[i + 1];

            // 计算转向角度
            let vec1 = Vector3D::new(
                (current.x - prev.x) as f32,
                (current.y - prev.y) as f32,
                (current.z - prev.z) as f32,
            );
            let vec2 = Vector3D::new(
                (next.x - current.x) as f32,
                (next.y - current.y) as f32,
                (next.z - current.z) as f32,
            );

            let angle = vec1.angle(&vec2);
            
            // 如果转向角度大于阈值，保留该点
            if angle > angle_threshold {
                filtered_waypoints.push(*current);
            }
        }

        filtered_waypoints.push(path.waypoints[path.waypoints.len() - 1]);

        let mut filtered_path = Path::new();
        filtered_path.waypoints = filtered_waypoints;
        filtered_path.total_cost = filtered_path.calculate_distance();
        filtered_path.planning_time_ms = path.planning_time_ms;

        filtered_path
    }
}
