use crate::types::{GridCoord, BoundingBox, MapConfig};
use fnv::FnvHashMap;
use std::collections::HashSet;

/// 八叉树节点
#[derive(Debug)]
pub struct OctreeNode {
    pub bounds: BoundingBox,
    pub objects: Vec<GridCoord>,
    pub children: Option<Box<[OctreeNode; 8]>>,
    pub max_objects: usize,
    pub max_depth: usize,
    pub depth: usize,
}

impl OctreeNode {
    pub fn new(bounds: BoundingBox, max_objects: usize, max_depth: usize, depth: usize) -> Self {
        Self {
            bounds,
            objects: Vec::new(),
            children: None,
            max_objects,
            max_depth,
            depth,
        }
    }

    /// 插入对象
    pub fn insert(&mut self, coord: GridCoord) {
        if !self.bounds.contains(&coord) {
            return;
        }

        if self.objects.len() < self.max_objects || self.depth >= self.max_depth {
            self.objects.push(coord);
            return;
        }

        if self.children.is_none() {
            self.subdivide();
        }

        if let Some(ref mut children) = self.children {
            for child in children.iter_mut() {
                child.insert(coord);
            }
        }
    }

    /// 细分节点
    fn subdivide(&mut self) {
        let mid_x = (self.bounds.min.x + self.bounds.max.x) / 2;
        let mid_y = (self.bounds.min.y + self.bounds.max.y) / 2;
        let mid_z = (self.bounds.min.z + self.bounds.max.z) / 2;

        let children = [
            // 前下左
            OctreeNode::new(
                BoundingBox::new(
                    self.bounds.min,
                    GridCoord::new(mid_x, mid_y, mid_z),
                ),
                self.max_objects,
                self.max_depth,
                self.depth + 1,
            ),
            // 前下右
            OctreeNode::new(
                BoundingBox::new(
                    GridCoord::new(mid_x + 1, self.bounds.min.y, self.bounds.min.z),
                    GridCoord::new(self.bounds.max.x, mid_y, mid_z),
                ),
                self.max_objects,
                self.max_depth,
                self.depth + 1,
            ),
            // 前上左
            OctreeNode::new(
                BoundingBox::new(
                    GridCoord::new(self.bounds.min.x, mid_y + 1, self.bounds.min.z),
                    GridCoord::new(mid_x, self.bounds.max.y, mid_z),
                ),
                self.max_objects,
                self.max_depth,
                self.depth + 1,
            ),
            // 前上右
            OctreeNode::new(
                BoundingBox::new(
                    GridCoord::new(mid_x + 1, mid_y + 1, self.bounds.min.z),
                    GridCoord::new(self.bounds.max.x, self.bounds.max.y, mid_z),
                ),
                self.max_objects,
                self.max_depth,
                self.depth + 1,
            ),
            // 后下左
            OctreeNode::new(
                BoundingBox::new(
                    GridCoord::new(self.bounds.min.x, self.bounds.min.y, mid_z + 1),
                    GridCoord::new(mid_x, mid_y, self.bounds.max.z),
                ),
                self.max_objects,
                self.max_depth,
                self.depth + 1,
            ),
            // 后下右
            OctreeNode::new(
                BoundingBox::new(
                    GridCoord::new(mid_x + 1, self.bounds.min.y, mid_z + 1),
                    GridCoord::new(self.bounds.max.x, mid_y, self.bounds.max.z),
                ),
                self.max_objects,
                self.max_depth,
                self.depth + 1,
            ),
            // 后上左
            OctreeNode::new(
                BoundingBox::new(
                    GridCoord::new(self.bounds.min.x, mid_y + 1, mid_z + 1),
                    GridCoord::new(mid_x, self.bounds.max.y, self.bounds.max.z),
                ),
                self.max_objects,
                self.max_depth,
                self.depth + 1,
            ),
            // 后上右
            OctreeNode::new(
                BoundingBox::new(
                    GridCoord::new(mid_x + 1, mid_y + 1, mid_z + 1),
                    self.bounds.max,
                ),
                self.max_objects,
                self.max_depth,
                self.depth + 1,
            ),
        ];

        self.children = Some(Box::new(children));

        // 重新分配现有对象
        let objects = std::mem::take(&mut self.objects);
        for obj in objects {
            if let Some(ref mut children) = self.children {
                for child in children.iter_mut() {
                    child.insert(obj);
                }
            }
        }
    }

    /// 查询范围内的对象
    pub fn query_range(&self, range: &BoundingBox, result: &mut Vec<GridCoord>) {
        if !self.bounds.intersects(range) {
            return;
        }

        for &obj in &self.objects {
            if range.contains(&obj) {
                result.push(obj);
            }
        }

        if let Some(ref children) = self.children {
            for child in children.iter() {
                child.query_range(range, result);
            }
        }
    }

    /// 查询最近邻
    pub fn query_nearest(&self, point: &GridCoord, max_distance: f32) -> Vec<GridCoord> {
        let mut result = Vec::new();
        let search_range = BoundingBox::new(
            GridCoord::new(
                point.x - max_distance as i32,
                point.y - max_distance as i32,
                point.z - max_distance as i32,
            ),
            GridCoord::new(
                point.x + max_distance as i32,
                point.y + max_distance as i32,
                point.z + max_distance as i32,
            ),
        );
        self.query_range(&search_range, &mut result);
        
        // 过滤距离并排序
        result.retain(|coord| coord.euclidean_distance(point) <= max_distance);
        result.sort_by(|a, b| {
            a.euclidean_distance(point)
                .partial_cmp(&b.euclidean_distance(point))
                .unwrap()
        });
        
        result
    }
}

/// 八叉树
#[derive(Debug)]
pub struct Octree {
    root: OctreeNode,
}

impl Octree {
    pub fn new(bounds: BoundingBox, max_objects: usize, max_depth: usize) -> Self {
        Self {
            root: OctreeNode::new(bounds, max_objects, max_depth, 0),
        }
    }

    pub fn insert(&mut self, coord: GridCoord) {
        self.root.insert(coord);
    }

    pub fn query_range(&self, range: &BoundingBox) -> Vec<GridCoord> {
        let mut result = Vec::new();
        self.root.query_range(range, &mut result);
        result
    }

    pub fn query_nearest(&self, point: &GridCoord, max_distance: f32) -> Vec<GridCoord> {
        self.root.query_nearest(point, max_distance)
    }
}

/// 空间哈希表 - 用于快速查找相邻网格
#[derive(Debug)]
pub struct SpatialHash {
    hash_map: FnvHashMap<GridCoord, HashSet<GridCoord>>,
    cell_size: i32,
}

impl SpatialHash {
    pub fn new(cell_size: i32) -> Self {
        Self {
            hash_map: FnvHashMap::default(),
            cell_size,
        }
    }

    /// 计算哈希键
    fn hash_key(&self, coord: &GridCoord) -> GridCoord {
        GridCoord::new(
            coord.x / self.cell_size,
            coord.y / self.cell_size,
            coord.z / self.cell_size,
        )
    }

    /// 插入对象
    pub fn insert(&mut self, coord: GridCoord) {
        let key = self.hash_key(&coord);
        self.hash_map.entry(key).or_insert_with(HashSet::new).insert(coord);
    }

    /// 移除对象
    pub fn remove(&mut self, coord: &GridCoord) {
        let key = self.hash_key(coord);
        if let Some(set) = self.hash_map.get_mut(&key) {
            set.remove(coord);
            if set.is_empty() {
                self.hash_map.remove(&key);
            }
        }
    }

    /// 查询附近的对象
    pub fn query_nearby(&self, coord: &GridCoord, radius: i32) -> Vec<GridCoord> {
        let mut result = Vec::new();
        let center_key = self.hash_key(coord);
        let search_radius = (radius / self.cell_size) + 1;

        for dx in -search_radius..=search_radius {
            for dy in -search_radius..=search_radius {
                for dz in -search_radius..=search_radius {
                    let search_key = GridCoord::new(
                        center_key.x + dx,
                        center_key.y + dy,
                        center_key.z + dz,
                    );

                    if let Some(objects) = self.hash_map.get(&search_key) {
                        for &obj in objects {
                            if obj.manhattan_distance(coord) <= radius {
                                result.push(obj);
                            }
                        }
                    }
                }
            }
        }

        result
    }

    /// 清空哈希表
    pub fn clear(&mut self) {
        self.hash_map.clear();
    }

    /// 获取统计信息
    pub fn stats(&self) -> (usize, usize) {
        let bucket_count = self.hash_map.len();
        let object_count = self.hash_map.values().map(|set| set.len()).sum();
        (bucket_count, object_count)
    }
}

/// 空间索引管理器
#[derive(Debug)]
pub struct SpatialIndexManager {
    octree: Octree,
    spatial_hash: SpatialHash,
    config: MapConfig,
}

impl SpatialIndexManager {
    pub fn new(config: MapConfig) -> Self {
        let bounds = BoundingBox::new(
            GridCoord::new(0, 0, 0),
            GridCoord::new(config.width - 1, config.height - 1, config.depth - 1),
        );

        Self {
            octree: Octree::new(bounds, 64, 8), // 最多64个对象，最大深度8
            spatial_hash: SpatialHash::new(100), // 100x100x100的哈希单元
            config,
        }
    }

    /// 添加障碍物到索引
    pub fn add_obstacle(&mut self, coord: GridCoord) {
        self.octree.insert(coord);
        self.spatial_hash.insert(coord);
    }

    /// 移除障碍物
    pub fn remove_obstacle(&mut self, coord: &GridCoord) {
        self.spatial_hash.remove(coord);
        // 注意：八叉树不支持删除，需要重建
    }

    /// 查询范围内的障碍物
    pub fn query_obstacles_in_range(&self, range: &BoundingBox) -> Vec<GridCoord> {
        self.octree.query_range(range)
    }

    /// 查询附近的障碍物
    pub fn query_nearby_obstacles(&self, coord: &GridCoord, radius: i32) -> Vec<GridCoord> {
        self.spatial_hash.query_nearby(coord, radius)
    }

    /// 重建八叉树（在大量删除操作后使用）
    pub fn rebuild_octree(&mut self, obstacles: &[GridCoord]) {
        let bounds = BoundingBox::new(
            GridCoord::new(0, 0, 0),
            GridCoord::new(self.config.width - 1, self.config.height - 1, self.config.depth - 1),
        );
        self.octree = Octree::new(bounds, 64, 8);
        
        for &obstacle in obstacles {
            self.octree.insert(obstacle);
        }
    }
}
