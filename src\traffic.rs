use crate::types::{GridCoord, Path, MapConfig};
use std::collections::HashMap;
use serde::{Deserialize, Serialize};

/// 时空路径点 - 包含时间信息的路径点
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TimedWaypoint {
    pub coord: GridCoord,
    pub timestamp: f32, // 到达该点的时间（秒）
}

impl TimedWaypoint {
    pub fn new(coord: GridCoord, timestamp: f32) -> Self {
        Self { coord, timestamp }
    }
}

/// 时空路径 - 包含时间信息的完整路径
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TimedPath {
    pub waypoints: Vec<TimedWaypoint>,
    pub drone_id: u32,
    pub start_time: f32,
    pub end_time: f32,
    pub safety_radius: f32, // 安全半径（米）
}

impl TimedPath {
    pub fn new(drone_id: u32, safety_radius: f32) -> Self {
        Self {
            waypoints: Vec::new(),
            drone_id,
            start_time: 0.0,
            end_time: 0.0,
            safety_radius,
        }
    }

    /// 从普通路径创建时空路径
    pub fn from_path(path: &Path, drone_id: u32, start_time: f32, speed: f32, safety_radius: f32) -> Self {
        let mut timed_path = Self::new(drone_id, safety_radius);
        timed_path.start_time = start_time;

        if path.waypoints.is_empty() {
            return timed_path;
        }

        let mut current_time = start_time;
        timed_path.waypoints.push(TimedWaypoint::new(path.waypoints[0], current_time));

        for i in 1..path.waypoints.len() {
            let distance = path.waypoints[i-1].euclidean_distance(&path.waypoints[i]);
            let travel_time = distance / speed;
            current_time += travel_time;
            timed_path.waypoints.push(TimedWaypoint::new(path.waypoints[i], current_time));
        }

        timed_path.end_time = current_time;
        timed_path
    }

    /// 获取指定时间的位置（线性插值）
    pub fn get_position_at_time(&self, time: f32) -> Option<GridCoord> {
        if time < self.start_time || time > self.end_time || self.waypoints.len() < 2 {
            return None;
        }

        // 找到时间区间
        for i in 1..self.waypoints.len() {
            let prev_waypoint = &self.waypoints[i-1];
            let curr_waypoint = &self.waypoints[i];

            if time >= prev_waypoint.timestamp && time <= curr_waypoint.timestamp {
                // 线性插值
                let time_ratio = (time - prev_waypoint.timestamp) / 
                               (curr_waypoint.timestamp - prev_waypoint.timestamp);
                
                let x = prev_waypoint.coord.x as f32 + 
                       (curr_waypoint.coord.x - prev_waypoint.coord.x) as f32 * time_ratio;
                let y = prev_waypoint.coord.y as f32 + 
                       (curr_waypoint.coord.y - prev_waypoint.coord.y) as f32 * time_ratio;
                let z = prev_waypoint.coord.z as f32 + 
                       (curr_waypoint.coord.z - prev_waypoint.coord.z) as f32 * time_ratio;

                return Some(GridCoord::new(x.round() as i32, y.round() as i32, z.round() as i32));
            }
        }

        None
    }

    /// 检查与另一条路径是否有时空冲突
    pub fn has_conflict_with(&self, other: &TimedPath, grid_size: f32) -> bool {
        // 检查时间重叠
        if self.end_time < other.start_time || other.end_time < self.start_time {
            return false;
        }

        let overlap_start = self.start_time.max(other.start_time);
        let overlap_end = self.end_time.min(other.end_time);
        let time_step = 0.1; // 每0.1秒检查一次

        let mut time = overlap_start;
        while time <= overlap_end {
            if let (Some(pos1), Some(pos2)) = (
                self.get_position_at_time(time),
                other.get_position_at_time(time)
            ) {
                let distance = pos1.euclidean_distance(&pos2) * grid_size;
                let required_distance = self.safety_radius + other.safety_radius;
                
                if distance < required_distance {
                    return true;
                }
            }
            time += time_step;
        }

        false
    }

    /// 获取路径占用的时空体素
    pub fn get_occupied_voxels(&self, grid_size: f32, _time_resolution: f32) -> Vec<(GridCoord, f32)> {
        let mut voxels = Vec::new();
        let safety_grid_radius = (self.safety_radius / grid_size).ceil() as i32;

        for waypoint in &self.waypoints {
            // 为每个路径点周围的安全区域创建体素
            for dx in -safety_grid_radius..=safety_grid_radius {
                for dy in -safety_grid_radius..=safety_grid_radius {
                    for dz in -safety_grid_radius..=safety_grid_radius {
                        let occupied_coord = GridCoord::new(
                            waypoint.coord.x + dx,
                            waypoint.coord.y + dy,
                            waypoint.coord.z + dz,
                        );
                        
                        let distance = waypoint.coord.euclidean_distance(&occupied_coord) * grid_size;
                        if distance <= self.safety_radius {
                            voxels.push((occupied_coord, waypoint.timestamp));
                        }
                    }
                }
            }
        }

        voxels
    }
}

/// 航线管理器
#[derive(Debug)]
pub struct TrafficManager {
    active_paths: HashMap<u32, TimedPath>,
    config: MapConfig,
    time_resolution: f32, // 时间分辨率（秒）
}

impl TrafficManager {
    pub fn new(config: MapConfig) -> Self {
        Self {
            active_paths: HashMap::new(),
            config,
            time_resolution: 0.1, // 0.1秒的时间分辨率
        }
    }

    /// 添加新的航线
    pub fn add_path(&mut self, path: TimedPath) -> Result<(), String> {
        // 检查与现有路径的冲突
        for existing_path in self.active_paths.values() {
            if path.has_conflict_with(existing_path, self.config.grid_size) {
                return Err(format!(
                    "Path conflicts with existing drone {} path", 
                    existing_path.drone_id
                ));
            }
        }

        self.active_paths.insert(path.drone_id, path);
        Ok(())
    }

    /// 移除航线
    pub fn remove_path(&mut self, drone_id: u32) -> Option<TimedPath> {
        self.active_paths.remove(&drone_id)
    }

    /// 检查指定时空位置是否被占用
    pub fn is_occupied(&self, coord: &GridCoord, time: f32, safety_radius: f32) -> bool {
        for path in self.active_paths.values() {
            if let Some(drone_pos) = path.get_position_at_time(time) {
                let distance = coord.euclidean_distance(&drone_pos) * self.config.grid_size;
                let required_distance = safety_radius + path.safety_radius;
                
                if distance < required_distance {
                    return true;
                }
            }
        }
        false
    }

    /// 获取指定时间范围内的所有冲突
    pub fn get_conflicts_in_timerange(&self, start_time: f32, end_time: f32) -> Vec<(u32, u32)> {
        let mut conflicts = Vec::new();
        let paths: Vec<_> = self.active_paths.values().collect();

        for i in 0..paths.len() {
            for j in (i+1)..paths.len() {
                let path1 = paths[i];
                let path2 = paths[j];

                // 检查时间范围重叠
                if path1.end_time >= start_time && path1.start_time <= end_time &&
                   path2.end_time >= start_time && path2.start_time <= end_time {
                    
                    if path1.has_conflict_with(path2, self.config.grid_size) {
                        conflicts.push((path1.drone_id, path2.drone_id));
                    }
                }
            }
        }

        conflicts
    }

    /// 清理过期的航线
    pub fn cleanup_expired_paths(&mut self, current_time: f32) {
        self.active_paths.retain(|_, path| path.end_time > current_time);
    }

    /// 获取指定区域内的活跃航线
    pub fn get_paths_in_region(&self, min_coord: &GridCoord, max_coord: &GridCoord, 
                              start_time: f32, end_time: f32) -> Vec<&TimedPath> {
        self.active_paths.values()
            .filter(|path| {
                // 检查时间重叠
                if path.end_time < start_time || path.start_time > end_time {
                    return false;
                }

                // 检查空间重叠
                for waypoint in &path.waypoints {
                    if waypoint.coord.x >= min_coord.x && waypoint.coord.x <= max_coord.x &&
                       waypoint.coord.y >= min_coord.y && waypoint.coord.y <= max_coord.y &&
                       waypoint.coord.z >= min_coord.z && waypoint.coord.z <= max_coord.z {
                        return true;
                    }
                }
                false
            })
            .collect()
    }

    /// 预测未来时间的交通密度
    pub fn predict_traffic_density(&self, coord: &GridCoord, time: f32, radius: i32) -> f32 {
        let mut drone_count = 0;
        let search_radius = radius as f32 * self.config.grid_size;

        for path in self.active_paths.values() {
            if let Some(drone_pos) = path.get_position_at_time(time) {
                let distance = coord.euclidean_distance(&drone_pos) * self.config.grid_size;
                if distance <= search_radius {
                    drone_count += 1;
                }
            }
        }

        // 返回密度（每立方公里的无人机数量）
        let volume = (4.0 / 3.0) * std::f32::consts::PI * search_radius.powi(3) / 1_000_000_000.0; // 转换为立方公里
        drone_count as f32 / volume
    }

    /// 获取统计信息
    pub fn get_stats(&self) -> (usize, f32, f32) {
        let active_count = self.active_paths.len();
        let avg_path_length = if active_count > 0 {
            self.active_paths.values()
                .map(|path| path.waypoints.len() as f32)
                .sum::<f32>() / active_count as f32
        } else {
            0.0
        };
        let avg_duration = if active_count > 0 {
            self.active_paths.values()
                .map(|path| path.end_time - path.start_time)
                .sum::<f32>() / active_count as f32
        } else {
            0.0
        };

        (active_count, avg_path_length, avg_duration)
    }

    /// 获取所有活跃路径的引用
    pub fn get_active_paths(&self) -> &HashMap<u32, TimedPath> {
        &self.active_paths
    }

    /// 检查路径是否可以安全添加（不实际添加）
    pub fn can_add_path(&self, path: &TimedPath) -> bool {
        for existing_path in self.active_paths.values() {
            if path.has_conflict_with(existing_path, self.config.grid_size) {
                return false;
            }
        }
        true
    }
}
