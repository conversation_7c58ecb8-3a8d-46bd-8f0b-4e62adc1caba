use crate::types::{GridCoord, MapConfig, Path};
use crate::obstacles::StaticObstacleMap;
use crate::adaptive_pathfinding::AdaptivePathfinder;
use std::io::{self, Write};

/// 简单的3D可视化器
pub struct Simple3DVisualizer {
    config: MapConfig,
    obstacles: StaticObstacleMap,
    path: Option<Path>,
}

impl Simple3DVisualizer {
    pub fn new() -> Self {
        let config = MapConfig {
            width: 10,
            height: 10,
            depth: 10,
            grid_size: 1.0,
            coarse_grid_factor: 2,
        };
        
        let obstacles = StaticObstacleMap::new(config.clone());
        
        Self {
            config,
            obstacles,
            path: None,
        }
    }

    /// 添加障碍物
    pub fn add_obstacle(&mut self, coord: GridCoord) {
        self.obstacles.set_obstacle(coord, true);
    }

    /// 添加一些示例障碍物
    pub fn add_sample_obstacles(&mut self) {
        // 创建一些有趣的障碍物模式
        
        // 中央柱子
        for z in 2..8 {
            self.add_obstacle(GridCoord::new(5, 5, z));
        }
        
        // L形障碍物
        for x in 2..6 {
            self.add_obstacle(GridCoord::new(x, 3, 4));
        }
        for y in 3..7 {
            self.add_obstacle(GridCoord::new(2, y, 4));
        }
        
        // 一些随机障碍物
        let obstacles = [
            GridCoord::new(7, 2, 3),
            GridCoord::new(8, 7, 6),
            GridCoord::new(3, 8, 2),
            GridCoord::new(6, 1, 7),
            GridCoord::new(1, 6, 5),
        ];
        
        for &obstacle in &obstacles {
            self.add_obstacle(obstacle);
        }
    }

    /// 运行路径规划
    pub fn plan_path(&mut self, start: GridCoord, goal: GridCoord) -> bool {
        println!("开始路径规划...");
        println!("起点: ({}, {}, {})", start.x, start.y, start.z);
        println!("终点: ({}, {}, {})", goal.x, goal.y, goal.z);
        
        let pathfinder = AdaptivePathfinder::new(self.config.clone(), &self.obstacles);
        let (node_count, avg_level) = pathfinder.get_grid_stats();
        println!("自适应网格: {} 个节点, 平均级别: {:.2}", node_count, avg_level);
        
        match pathfinder.find_path(start, goal) {
            Some(path) => {
                println!("✅ 路径规划成功!");
                println!("路径长度: {} 个点", path.waypoints.len());
                println!("总距离: {:.2}", path.total_cost);
                println!("规划时间: {:.2}ms", path.planning_time_ms);
                self.path = Some(path);
                true
            }
            None => {
                println!("❌ 路径规划失败!");
                false
            }
        }
    }

    /// 显示2D切片视图
    pub fn display_slice(&self, z_level: i32) {
        println!("\n=== Z={} 层视图 ===", z_level);
        
        // 打印列标题
        print!("   ");
        for x in 0..self.config.width {
            print!("{:2}", x);
        }
        println!();
        
        for y in 0..self.config.height {
            print!("{:2} ", y);
            for x in 0..self.config.width {
                let coord = GridCoord::new(x, y, z_level);
                let symbol = self.get_cell_symbol(&coord);
                print!("{:2}", symbol);
            }
            println!();
        }
        
        println!("图例: ■=障碍物, ●=路径, S=起点, G=终点, ·=空地");
    }

    /// 获取单元格显示符号
    fn get_cell_symbol(&self, coord: &GridCoord) -> char {
        // 检查是否为障碍物
        if self.obstacles.is_obstacle(coord) {
            return '■';
        }
        
        // 检查是否在路径上
        if let Some(ref path) = self.path {
            if let Some(pos) = path.waypoints.iter().position(|&p| p == *coord) {
                if pos == 0 {
                    return 'S'; // 起点
                } else if pos == path.waypoints.len() - 1 {
                    return 'G'; // 终点
                } else {
                    return '●'; // 路径点
                }
            }
        }
        
        '·' // 空地
    }

    /// 交互式浏览
    pub fn interactive_browse(&self) {
        let mut current_z = 0;
        
        loop {
            // 清屏（在支持的终端中）
            print!("\x1B[2J\x1B[1;1H");
            
            self.display_slice(current_z);
            
            println!("\n命令:");
            println!("  w/s: 上/下移动层级");
            println!("  q: 退出");
            println!("  当前层级: {}/{}", current_z, self.config.depth - 1);
            
            print!("输入命令: ");
            io::stdout().flush().unwrap();
            
            let mut input = String::new();
            if io::stdin().read_line(&mut input).is_ok() {
                match input.trim() {
                    "w" | "W" => {
                        if current_z < self.config.depth - 1 {
                            current_z += 1;
                        }
                    }
                    "s" | "S" => {
                        if current_z > 0 {
                            current_z -= 1;
                        }
                    }
                    "q" | "Q" => break,
                    _ => {
                        println!("无效命令!");
                        std::thread::sleep(std::time::Duration::from_millis(1000));
                    }
                }
            }
        }
    }

    /// 显示路径统计
    pub fn display_path_stats(&self) {
        if let Some(ref path) = self.path {
            println!("\n=== 路径统计 ===");
            println!("路径点数量: {}", path.waypoints.len());
            println!("总距离: {:.2}", path.total_cost);
            println!("规划时间: {:.2}ms", path.planning_time_ms);
            
            println!("\n路径详细:");
            for (i, &point) in path.waypoints.iter().enumerate() {
                if i == 0 {
                    println!("  起点: ({}, {}, {})", point.x, point.y, point.z);
                } else if i == path.waypoints.len() - 1 {
                    println!("  终点: ({}, {}, {})", point.x, point.y, point.z);
                } else if i < 10 || i >= path.waypoints.len() - 5 {
                    println!("  {}: ({}, {}, {})", i, point.x, point.y, point.z);
                } else if i == 10 {
                    println!("  ... (省略中间点) ...");
                }
            }
        }
    }

    /// 显示所有层的概览
    pub fn display_overview(&self) {
        println!("\n=== 3D概览 ===");
        for z in 0..self.config.depth {
            println!("\n--- Z={} ---", z);
            for y in 0..self.config.height {
                for x in 0..self.config.width {
                    let coord = GridCoord::new(x, y, z);
                    print!("{}", self.get_cell_symbol(&coord));
                }
                println!();
            }
        }
    }

    /// 分析障碍物分布
    pub fn analyze_obstacles(&self) {
        let mut obstacle_count = 0;
        let mut layer_counts = vec![0; self.config.depth as usize];
        
        for z in 0..self.config.depth {
            for y in 0..self.config.height {
                for x in 0..self.config.width {
                    let coord = GridCoord::new(x, y, z);
                    if self.obstacles.is_obstacle(&coord) {
                        obstacle_count += 1;
                        layer_counts[z as usize] += 1;
                    }
                }
            }
        }
        
        let total_cells = self.config.width * self.config.height * self.config.depth;
        let density = obstacle_count as f32 / total_cells as f32 * 100.0;
        
        println!("\n=== 障碍物分析 ===");
        println!("总障碍物数量: {}", obstacle_count);
        println!("总网格数量: {}", total_cells);
        println!("障碍物密度: {:.1}%", density);
        
        println!("\n各层障碍物分布:");
        for (z, &count) in layer_counts.iter().enumerate() {
            let layer_density = count as f32 / (self.config.width * self.config.height) as f32 * 100.0;
            println!("  Z={}: {} 个 ({:.1}%)", z, count, layer_density);
        }
    }
}

/// 运行简单测试
pub fn run_simple_test() {
    println!("🚁 无人机3D路径规划 - 简单测试 (10×10×10)");
    println!("=" .repeat(50));
    
    let mut visualizer = Simple3DVisualizer::new();
    
    // 添加示例障碍物
    visualizer.add_sample_obstacles();
    
    // 分析障碍物
    visualizer.analyze_obstacles();
    
    // 设置起点和终点
    let start = GridCoord::new(0, 0, 0);
    let goal = GridCoord::new(9, 9, 9);
    
    // 规划路径
    if visualizer.plan_path(start, goal) {
        // 显示路径统计
        visualizer.display_path_stats();
        
        // 交互式浏览
        println!("\n按回车键开始交互式浏览...");
        let mut input = String::new();
        io::stdin().read_line(&mut input).unwrap();
        
        visualizer.interactive_browse();
    }
    
    println!("\n测试完成!");
}
