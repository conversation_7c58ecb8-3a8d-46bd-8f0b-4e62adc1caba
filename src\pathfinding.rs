use crate::types::{GridCoord, Path, MapConfig, BoundingBox};
use crate::obstacles::{StaticObstacleMap, DynamicObstacleManager};
use crate::spatial_index::SpatialIndexManager;
use priority_queue::PriorityQueue;
use std::collections::{HashMap, HashSet};
use std::cmp::Ordering;
use std::time::Instant;

/// 路径规划结果
#[derive(Debug)]
pub enum PlanningResult {
    Success(Path),
    NoPathFound,
    Timeout,
    InvalidInput,
}

/// A*算法的优先级包装器
#[derive(Debug, Clone, Hash)]
struct PriorityWrapper {
    f_cost: i32, // 使用整数避免浮点数Hash问题
    coord: GridCoord,
}

impl PriorityWrapper {
    fn new(f_cost: f32, coord: GridCoord) -> Self {
        Self {
            f_cost: (f_cost * 1000.0) as i32, // 转换为整数，保留3位小数精度
            coord,
        }
    }

    fn get_f_cost(&self) -> f32 {
        self.f_cost as f32 / 1000.0
    }
}

impl PartialEq for PriorityWrapper {
    fn eq(&self, other: &Self) -> bool {
        self.f_cost == other.f_cost && self.coord == other.coord
    }
}

impl Eq for PriorityWrapper {}

impl PartialOrd for PriorityWrapper {
    fn partial_cmp(&self, other: &Self) -> Option<Ordering> {
        // 注意：PriorityQueue是最大堆，我们需要最小堆，所以反转比较
        other.f_cost.partial_cmp(&self.f_cost)
    }
}

impl Ord for PriorityWrapper {
    fn cmp(&self, other: &Self) -> Ordering {
        // 先比较f_cost，再比较coord确保一致性
        match other.f_cost.cmp(&self.f_cost) {
            Ordering::Equal => self.coord.cmp(&other.coord),
            other => other,
        }
    }
}

/// 分层A*路径规划器
pub struct HierarchicalAStar {
    config: MapConfig,
    static_obstacles: StaticObstacleMap,
    dynamic_obstacles: DynamicObstacleManager,
    spatial_index: SpatialIndexManager,
    max_planning_time_ms: u64,
}

impl HierarchicalAStar {
    pub fn new(config: MapConfig) -> Self {
        Self {
            static_obstacles: StaticObstacleMap::new(config.clone()),
            dynamic_obstacles: DynamicObstacleManager::new(config.clone()),
            spatial_index: SpatialIndexManager::new(config.clone()),
            config,
            max_planning_time_ms: 10, // 10毫秒超时
        }
    }

    /// 设置最大规划时间
    pub fn set_max_planning_time(&mut self, time_ms: u64) {
        self.max_planning_time_ms = time_ms;
    }

    /// 添加静态障碍物
    pub fn add_static_obstacle(&mut self, coord: GridCoord) {
        self.static_obstacles.set_obstacle(coord, true);
        self.spatial_index.add_obstacle(coord);
    }

    /// 批量添加静态障碍物
    pub fn add_static_obstacles_batch(&mut self, coords: &[GridCoord]) {
        self.static_obstacles.set_obstacles_batch(coords, true);
        for &coord in coords {
            self.spatial_index.add_obstacle(coord);
        }
    }

    /// 检查坐标是否为障碍物
    fn is_obstacle(&self, coord: &GridCoord) -> bool {
        if !self.config.is_valid_coord(coord) {
            return true;
        }

        // 检查静态障碍物
        if self.static_obstacles.is_obstacle(coord) {
            return true;
        }

        // 检查动态禁飞区
        if self.dynamic_obstacles.is_in_no_fly_zone(coord) {
            return true;
        }

        false
    }

    /// 计算启发式函数值
    fn heuristic(&self, current: &GridCoord, goal: &GridCoord) -> f32 {
        let base_distance = current.euclidean_distance(goal);
        
        // 添加障碍物密度权重
        let obstacle_density = self.static_obstacles.get_obstacle_density(current, 3);
        let density_weight = 1.0 + obstacle_density * 2.0;
        
        base_distance * density_weight
    }

    /// 粗粒度A*搜索
    fn coarse_astar(&self, start: GridCoord, goal: GridCoord, start_time: Instant) -> Option<Vec<GridCoord>> {
        let coarse_start = self.config.to_coarse_coord(&start);
        let coarse_goal = self.config.to_coarse_coord(&goal);

        if coarse_start == coarse_goal {
            return Some(vec![coarse_start]);
        }

        let mut open_set = PriorityQueue::new();
        let mut came_from: HashMap<GridCoord, GridCoord> = HashMap::new();
        let mut g_score: HashMap<GridCoord, f32> = HashMap::new();
        let mut closed_set: HashSet<GridCoord> = HashSet::new();

        g_score.insert(coarse_start, 0.0);
        let h_cost = self.heuristic(&coarse_start, &coarse_goal);
        let wrapper = PriorityWrapper::new(h_cost, coarse_start);
        open_set.push(wrapper.clone(), wrapper);

        while let Some((current_wrapper, _)) = open_set.pop() {
            // 检查超时
            // if start_time.elapsed().as_millis() > self.max_planning_time_ms as u128 / 2 {
            //     break;
            // }

            let current = current_wrapper.coord;

            if current == coarse_goal {
                // 重构路径
                let mut path = vec![current];
                let mut current_node = current;
                while let Some(&parent) = came_from.get(&current_node) {
                    path.push(parent);
                    current_node = parent;
                }
                path.reverse();
                return Some(path);
            }

            closed_set.insert(current);

            // 检查相邻节点
            for neighbor in current.neighbors() {
                if closed_set.contains(&neighbor) {
                    continue;
                }

                // 检查粗粒度网格是否有障碍物
                let fine_bbox = self.config.from_coarse_coord(&neighbor);
                if self.is_coarse_cell_blocked(&fine_bbox) {
                    continue;
                }

                let tentative_g_score = g_score.get(&current).unwrap_or(&f32::INFINITY) + 
                                       current.euclidean_distance(&neighbor);

                if tentative_g_score < *g_score.get(&neighbor).unwrap_or(&f32::INFINITY) {
                    came_from.insert(neighbor, current);
                    g_score.insert(neighbor, tentative_g_score);
                    let f_score = tentative_g_score + self.heuristic(&neighbor, &coarse_goal);

                    let wrapper = PriorityWrapper::new(f_score, neighbor);
                    open_set.push(wrapper.clone(), wrapper);
                }
            }
        }

        None
    }

    /// 检查粗粒度网格是否被阻塞
    fn is_coarse_cell_blocked(&self, bbox: &BoundingBox) -> bool {
        let mut blocked_count = 0;
        let mut total_count = 0;

        // 采样检查，不需要检查每个网格
        let step = self.config.coarse_grid_factor / 4; // 每4个网格采样一次
        let step = step.max(1);

        for x in (bbox.min.x..=bbox.max.x).step_by(step as usize) {
            for y in (bbox.min.y..=bbox.max.y).step_by(step as usize) {
                for z in (bbox.min.z..=bbox.max.z).step_by(step as usize) {
                    let coord = GridCoord::new(x, y, z);
                    total_count += 1;
                    if self.is_obstacle(&coord) {
                        blocked_count += 1;
                    }
                }
            }
        }

        // 如果超过50%的采样点被阻塞，认为整个粗粒度网格被阻塞
        blocked_count as f32 / total_count as f32 > 0.5
    }

    /// 优化的A*算法 - 结合跳跃和传统搜索
    fn optimized_fine_astar(&self, start: GridCoord, goal: GridCoord, coarse_path: &[GridCoord], start_time: Instant) -> Option<Vec<GridCoord>> {
        let mut open_set = PriorityQueue::new();
        let mut came_from: HashMap<GridCoord, GridCoord> = HashMap::new();
        let mut g_score: HashMap<GridCoord, f32> = HashMap::new();
        let mut closed_set: HashSet<GridCoord> = HashSet::new();

        g_score.insert(start, 0.0);
        let h_cost = self.heuristic(&start, &goal);
        let wrapper = PriorityWrapper::new(h_cost, start);
        open_set.push(wrapper.clone(), wrapper);

        while let Some((current_wrapper, _)) = open_set.pop() {
            // 检查超时
            if start_time.elapsed().as_millis() > self.max_planning_time_ms as u128 {
                break;
            }

            let current = current_wrapper.coord;

            if current == goal {
                // 重构路径
                let mut path = vec![current];
                let mut current_node = current;
                while let Some(&parent) = came_from.get(&current_node) {
                    path.push(parent);
                    current_node = parent;
                }
                path.reverse();
                return Some(path);
            }

            closed_set.insert(current);

            // 使用智能邻居选择 - 优先选择朝向目标的方向
            let neighbors = self.get_smart_neighbors(&current, &goal, coarse_path);

            for neighbor in neighbors {
                if closed_set.contains(&neighbor) || self.is_obstacle(&neighbor) {
                    continue;
                }

                // 检查是否在粗粒度路径走廊内
                if !self.is_in_coarse_path_corridor(&neighbor, coarse_path) {
                    continue;
                }

                let tentative_g_score = g_score.get(&current).unwrap_or(&f32::INFINITY) +
                                       current.euclidean_distance(&neighbor);

                if tentative_g_score < *g_score.get(&neighbor).unwrap_or(&f32::INFINITY) {
                    came_from.insert(neighbor, current);
                    g_score.insert(neighbor, tentative_g_score);
                    let f_score = tentative_g_score + self.heuristic(&neighbor, &goal);

                    let wrapper = PriorityWrapper::new(f_score, neighbor);
                    open_set.push(wrapper.clone(), wrapper);
                }
            }
        }

        None
    }

    /// 智能邻居选择 - 优先选择朝向目标的方向
    fn get_smart_neighbors(&self, current: &GridCoord, goal: &GridCoord, _coarse_path: &[GridCoord]) -> Vec<GridCoord> {
        let mut neighbors = Vec::new();

        // 计算朝向目标的方向向量
        let dx = (goal.x - current.x).signum();
        let dy = (goal.y - current.y).signum();
        let dz = (goal.z - current.z).signum();

        // 优先级1：直接朝向目标的方向
        if dx != 0 || dy != 0 || dz != 0 {
            neighbors.push(GridCoord::new(current.x + dx, current.y + dy, current.z + dz));
        }

        // 优先级2：单轴朝向目标的方向
        if dx != 0 {
            neighbors.push(GridCoord::new(current.x + dx, current.y, current.z));
        }
        if dy != 0 {
            neighbors.push(GridCoord::new(current.x, current.y + dy, current.z));
        }
        if dz != 0 {
            neighbors.push(GridCoord::new(current.x, current.y, current.z + dz));
        }

        // 优先级3：双轴朝向目标的方向
        if dx != 0 && dy != 0 {
            neighbors.push(GridCoord::new(current.x + dx, current.y + dy, current.z));
        }
        if dx != 0 && dz != 0 {
            neighbors.push(GridCoord::new(current.x + dx, current.y, current.z + dz));
        }
        if dy != 0 && dz != 0 {
            neighbors.push(GridCoord::new(current.x, current.y + dy, current.z + dz));
        }

        // 优先级4：其他相邻方向（用于绕过障碍物）
        for dx_offset in -1..=1 {
            for dy_offset in -1..=1 {
                for dz_offset in -1..=1 {
                    if dx_offset == 0 && dy_offset == 0 && dz_offset == 0 {
                        continue;
                    }

                    let neighbor = GridCoord::new(
                        current.x + dx_offset,
                        current.y + dy_offset,
                        current.z + dz_offset,
                    );

                    if !neighbors.contains(&neighbor) {
                        neighbors.push(neighbor);
                    }
                }
            }
        }

        neighbors
    }

    /// 检查点是否在粗粒度路径走廊内
    fn is_in_coarse_path_corridor(&self, coord: &GridCoord, coarse_path: &[GridCoord]) -> bool {
        let coarse_coord = self.config.to_coarse_coord(coord);

        // 检查是否在粗粒度路径上或其相邻区域
        for &path_coord in coarse_path {
            if coarse_coord == path_coord || coarse_coord.manhattan_distance(&path_coord) <= 2 {
                return true;
            }
        }
        false
    }

    /// 主要的路径规划函数
    pub fn find_path(&self, start: GridCoord, goal: GridCoord) -> PlanningResult {
        let start_time = Instant::now();

        // 验证输入
        if !self.config.is_valid_coord(&start) || !self.config.is_valid_coord(&goal) {
            return PlanningResult::InvalidInput;
        }

        if self.is_obstacle(&start) || self.is_obstacle(&goal) {
            return PlanningResult::NoPathFound;
        }

        if start == goal {
            let mut path = Path::new();
            path.waypoints = vec![start];
            path.total_cost = 0.0;
            path.planning_time_ms = start_time.elapsed().as_millis() as f32;
            return PlanningResult::Success(path);
        }

        // 第一阶段：粗粒度搜索
        let coarse_path = match self.coarse_astar(start, goal, start_time) {
            Some(path) => path,
            None => return PlanningResult::NoPathFound,
        };

        // 第二阶段：优化的A*细粒度搜索
        let fine_path = match self.optimized_fine_astar(start, goal, &coarse_path, start_time) {
            Some(path) => path,
            None => return PlanningResult::NoPathFound,
        };

        // 构建结果
        let mut result_path = Path::new();
        result_path.waypoints = fine_path;
        result_path.total_cost = result_path.calculate_distance();
        result_path.planning_time_ms = start_time.elapsed().as_millis() as f32;

        // 检查是否超时
        if result_path.planning_time_ms > self.max_planning_time_ms as f32 {
            PlanningResult::Timeout
        } else {
            PlanningResult::Success(result_path)
        }
    }

    /// 获取动态障碍物管理器的可变引用
    pub fn get_dynamic_obstacles_mut(&mut self) -> &mut DynamicObstacleManager {
        &mut self.dynamic_obstacles
    }

    /// 获取静态障碍物地图的可变引用
    pub fn get_static_obstacles_mut(&mut self) -> &mut StaticObstacleMap {
        &mut self.static_obstacles
    }

    /// 获取静态障碍物地图的不可变引用
    pub fn get_static_obstacles(&self) -> &StaticObstacleMap {
        &self.static_obstacles
    }

    /// 获取动态障碍物管理器的不可变引用
    pub fn get_dynamic_obstacles(&self) -> &DynamicObstacleManager {
        &self.dynamic_obstacles
    }
}
