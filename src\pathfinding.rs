use crate::types::{GridCoord, Path, MapConfig, BoundingBox};
use crate::obstacles::{StaticObstacleMap, DynamicObstacleManager};
use crate::spatial_index::SpatialIndexManager;
use priority_queue::PriorityQueue;
use std::collections::{HashMap, HashSet};
use std::cmp::Ordering;
use std::time::Instant;

/// 路径规划结果
#[derive(Debug)]
pub enum PlanningResult {
    Success(Path),
    NoPathFound,
    Timeout,
    InvalidInput,
}

/// A*算法的优先级包装器
#[derive(Debug, Clone, Hash)]
struct PriorityWrapper {
    f_cost: i32, // 使用整数避免浮点数Hash问题
    coord: GridCoord,
}

impl PriorityWrapper {
    fn new(f_cost: f32, coord: GridCoord) -> Self {
        Self {
            f_cost: (f_cost * 1000.0) as i32, // 转换为整数，保留3位小数精度
            coord,
        }
    }

    fn get_f_cost(&self) -> f32 {
        self.f_cost as f32 / 1000.0
    }
}

impl PartialEq for PriorityWrapper {
    fn eq(&self, other: &Self) -> bool {
        self.f_cost == other.f_cost && self.coord == other.coord
    }
}

impl Eq for PriorityWrapper {}

impl PartialOrd for PriorityWrapper {
    fn partial_cmp(&self, other: &Self) -> Option<Ordering> {
        // 注意：PriorityQueue是最大堆，我们需要最小堆，所以反转比较
        other.f_cost.partial_cmp(&self.f_cost)
    }
}

impl Ord for PriorityWrapper {
    fn cmp(&self, other: &Self) -> Ordering {
        // 先比较f_cost，再比较coord确保一致性
        match other.f_cost.cmp(&self.f_cost) {
            Ordering::Equal => self.coord.cmp(&other.coord),
            other => other,
        }
    }
}

/// 分层A*路径规划器
pub struct HierarchicalAStar {
    config: MapConfig,
    static_obstacles: StaticObstacleMap,
    dynamic_obstacles: DynamicObstacleManager,
    spatial_index: SpatialIndexManager,
    max_planning_time_ms: u64,
}

impl HierarchicalAStar {
    pub fn new(config: MapConfig) -> Self {
        Self {
            static_obstacles: StaticObstacleMap::new(config.clone()),
            dynamic_obstacles: DynamicObstacleManager::new(config.clone()),
            spatial_index: SpatialIndexManager::new(config.clone()),
            config,
            max_planning_time_ms: 10, // 10毫秒超时
        }
    }

    /// 设置最大规划时间
    pub fn set_max_planning_time(&mut self, time_ms: u64) {
        self.max_planning_time_ms = time_ms;
    }

    /// 添加静态障碍物
    pub fn add_static_obstacle(&mut self, coord: GridCoord) {
        self.static_obstacles.set_obstacle(coord, true);
        self.spatial_index.add_obstacle(coord);
    }

    /// 批量添加静态障碍物
    pub fn add_static_obstacles_batch(&mut self, coords: &[GridCoord]) {
        self.static_obstacles.set_obstacles_batch(coords, true);
        for &coord in coords {
            self.spatial_index.add_obstacle(coord);
        }
    }

    /// 检查坐标是否为障碍物
    fn is_obstacle(&self, coord: &GridCoord) -> bool {
        if !self.config.is_valid_coord(coord) {
            return true;
        }

        // 检查静态障碍物
        if self.static_obstacles.is_obstacle(coord) {
            return true;
        }

        // 检查动态禁飞区
        if self.dynamic_obstacles.is_in_no_fly_zone(coord) {
            return true;
        }

        false
    }

    /// 计算启发式函数值
    fn heuristic(&self, current: &GridCoord, goal: &GridCoord) -> f32 {
        let base_distance = current.euclidean_distance(goal);
        
        // 添加障碍物密度权重
        let obstacle_density = self.static_obstacles.get_obstacle_density(current, 3);
        let density_weight = 1.0 + obstacle_density * 2.0;
        
        base_distance * density_weight
    }

    /// 粗粒度A*搜索
    fn coarse_astar(&self, start: GridCoord, goal: GridCoord, start_time: Instant) -> Option<Vec<GridCoord>> {
        let coarse_start = self.config.to_coarse_coord(&start);
        let coarse_goal = self.config.to_coarse_coord(&goal);

        if coarse_start == coarse_goal {
            return Some(vec![coarse_start]);
        }

        let mut open_set = PriorityQueue::new();
        let mut came_from: HashMap<GridCoord, GridCoord> = HashMap::new();
        let mut g_score: HashMap<GridCoord, f32> = HashMap::new();
        let mut closed_set: HashSet<GridCoord> = HashSet::new();

        g_score.insert(coarse_start, 0.0);
        let h_cost = self.heuristic(&coarse_start, &coarse_goal);
        let wrapper = PriorityWrapper::new(h_cost, coarse_start);
        open_set.push(wrapper.clone(), wrapper);

        while let Some((current_wrapper, _)) = open_set.pop() {
            // 检查超时
            if start_time.elapsed().as_millis() > self.max_planning_time_ms as u128 / 2 {
                break;
            }

            let current = current_wrapper.coord;

            if current == coarse_goal {
                // 重构路径
                let mut path = vec![current];
                let mut current_node = current;
                while let Some(&parent) = came_from.get(&current_node) {
                    path.push(parent);
                    current_node = parent;
                }
                path.reverse();
                return Some(path);
            }

            closed_set.insert(current);

            // 检查相邻节点
            for neighbor in current.neighbors() {
                if closed_set.contains(&neighbor) {
                    continue;
                }

                // 检查粗粒度网格是否有障碍物
                let fine_bbox = self.config.from_coarse_coord(&neighbor);
                if self.is_coarse_cell_blocked(&fine_bbox) {
                    continue;
                }

                let tentative_g_score = g_score.get(&current).unwrap_or(&f32::INFINITY) + 
                                       current.euclidean_distance(&neighbor);

                if tentative_g_score < *g_score.get(&neighbor).unwrap_or(&f32::INFINITY) {
                    came_from.insert(neighbor, current);
                    g_score.insert(neighbor, tentative_g_score);
                    let f_score = tentative_g_score + self.heuristic(&neighbor, &coarse_goal);

                    let wrapper = PriorityWrapper::new(f_score, neighbor);
                    open_set.push(wrapper.clone(), wrapper);
                }
            }
        }

        None
    }

    /// 检查粗粒度网格是否被阻塞
    fn is_coarse_cell_blocked(&self, bbox: &BoundingBox) -> bool {
        let mut blocked_count = 0;
        let mut total_count = 0;

        // 采样检查，不需要检查每个网格
        let step = self.config.coarse_grid_factor / 4; // 每4个网格采样一次
        let step = step.max(1);

        for x in (bbox.min.x..=bbox.max.x).step_by(step as usize) {
            for y in (bbox.min.y..=bbox.max.y).step_by(step as usize) {
                for z in (bbox.min.z..=bbox.max.z).step_by(step as usize) {
                    let coord = GridCoord::new(x, y, z);
                    total_count += 1;
                    if self.is_obstacle(&coord) {
                        blocked_count += 1;
                    }
                }
            }
        }

        // 如果超过50%的采样点被阻塞，认为整个粗粒度网格被阻塞
        blocked_count as f32 / total_count as f32 > 0.5
    }

    /// 3D Jump Point Search算法
    fn fine_jps(&self, start: GridCoord, goal: GridCoord, coarse_path: &[GridCoord], start_time: Instant) -> Option<Vec<GridCoord>> {
        let mut open_set = PriorityQueue::new();
        let mut came_from: HashMap<GridCoord, GridCoord> = HashMap::new();
        let mut g_score: HashMap<GridCoord, f32> = HashMap::new();
        let mut closed_set: HashSet<GridCoord> = HashSet::new();

        g_score.insert(start, 0.0);
        let h_cost = self.heuristic(&start, &goal);
        let wrapper = PriorityWrapper::new(h_cost, start);
        open_set.push(wrapper.clone(), wrapper);

        while let Some((current_wrapper, _)) = open_set.pop() {
            // 检查超时
            if start_time.elapsed().as_millis() > self.max_planning_time_ms as u128 {
                break;
            }

            let current = current_wrapper.coord;

            if current == goal {
                // 重构路径
                let mut path = vec![current];
                let mut current_node = current;
                while let Some(&parent) = came_from.get(&current_node) {
                    path.push(parent);
                    current_node = parent;
                }
                path.reverse();
                return Some(path);
            }

            closed_set.insert(current);

            // 获取跳跃点邻居
            let jump_neighbors = self.get_jump_neighbors(&current, &came_from, &goal, coarse_path, start_time);

            for jump_point in jump_neighbors {
                if closed_set.contains(&jump_point) {
                    continue;
                }

                let tentative_g_score = g_score.get(&current).unwrap_or(&f32::INFINITY) +
                                       current.euclidean_distance(&jump_point);

                if tentative_g_score < *g_score.get(&jump_point).unwrap_or(&f32::INFINITY) {
                    came_from.insert(jump_point, current);
                    g_score.insert(jump_point, tentative_g_score);
                    let f_score = tentative_g_score + self.heuristic(&jump_point, &goal);

                    let wrapper = PriorityWrapper::new(f_score, jump_point);
                    open_set.push(wrapper.clone(), wrapper);
                }
            }
        }

        None
    }

    /// 获取跳跃点邻居
    fn get_jump_neighbors(&self, current: &GridCoord, came_from: &HashMap<GridCoord, GridCoord>,
                         goal: &GridCoord, coarse_path: &[GridCoord], start_time: Instant) -> Vec<GridCoord> {
        let mut jump_neighbors = Vec::new();

        // 获取搜索方向
        let directions = if let Some(&parent) = came_from.get(current) {
            self.get_natural_neighbors(current, &parent)
        } else {
            // 起始点，搜索所有方向
            self.get_all_directions()
        };

        for direction in directions {
            if let Some(jump_point) = self.jump(current, &direction, goal, coarse_path, start_time) {
                jump_neighbors.push(jump_point);
            }
        }

        jump_neighbors
    }

    /// 跳跃搜索
    fn jump(&self, current: &GridCoord, direction: &GridCoord, goal: &GridCoord,
           coarse_path: &[GridCoord], start_time: Instant) -> Option<GridCoord> {
        // 检查超时
        if start_time.elapsed().as_millis() > self.max_planning_time_ms as u128 {
            return None;
        }

        let next = GridCoord::new(
            current.x + direction.x,
            current.y + direction.y,
            current.z + direction.z,
        );

        // 检查边界和障碍物
        if !self.config.is_valid_coord(&next) || self.is_obstacle(&next) {
            return None;
        }

        // 检查是否在粗粒度路径走廊内
        if !self.is_in_coarse_path_corridor(&next, coarse_path) {
            return None;
        }

        // 到达目标
        if next == *goal {
            return Some(next);
        }

        // 检查是否为跳跃点
        if self.is_3d_jump_point(&next, direction) {
            return Some(next);
        }

        // 对于对角线方向，需要检查组成方向
        if self.is_diagonal_direction(direction) {
            let component_directions = self.get_component_directions(direction);
            for comp_dir in component_directions {
                if self.jump(&next, &comp_dir, goal, coarse_path, start_time).is_some() {
                    return Some(next);
                }
            }
        }

        // 递归跳跃
        self.jump(&next, direction, goal, coarse_path, start_time)
    }

    /// 检查是否为3D跳跃点
    fn is_3d_jump_point(&self, coord: &GridCoord, direction: &GridCoord) -> bool {
        // 检查强制邻居
        let forced_neighbors = self.get_forced_neighbors(coord, direction);
        !forced_neighbors.is_empty()
    }

    /// 获取强制邻居
    fn get_forced_neighbors(&self, coord: &GridCoord, direction: &GridCoord) -> Vec<GridCoord> {
        let mut forced = Vec::new();

        // 获取垂直于移动方向的所有方向
        let perpendicular_dirs = self.get_perpendicular_directions_3d(direction);

        for perp_dir in perpendicular_dirs {
            let neighbor = GridCoord::new(
                coord.x + perp_dir.x,
                coord.y + perp_dir.y,
                coord.z + perp_dir.z,
            );

            let blocked_neighbor = GridCoord::new(
                coord.x - direction.x + perp_dir.x,
                coord.y - direction.y + perp_dir.y,
                coord.z - direction.z + perp_dir.z,
            );

            // 如果垂直方向的邻居可通行，但其后方被阻塞，则为强制邻居
            if self.config.is_valid_coord(&neighbor) &&
               !self.is_obstacle(&neighbor) &&
               (self.is_obstacle(&blocked_neighbor) || !self.config.is_valid_coord(&blocked_neighbor)) {
                forced.push(neighbor);
            }
        }

        forced
    }

    /// 获取3D垂直方向
    fn get_perpendicular_directions_3d(&self, direction: &GridCoord) -> Vec<GridCoord> {
        let mut perpendicular = Vec::new();

        // 对于每个非零分量，添加其垂直方向
        if direction.x != 0 {
            perpendicular.extend_from_slice(&[
                GridCoord::new(0, 1, 0), GridCoord::new(0, -1, 0),
                GridCoord::new(0, 0, 1), GridCoord::new(0, 0, -1),
                GridCoord::new(0, 1, 1), GridCoord::new(0, 1, -1),
                GridCoord::new(0, -1, 1), GridCoord::new(0, -1, -1),
            ]);
        }
        if direction.y != 0 {
            perpendicular.extend_from_slice(&[
                GridCoord::new(1, 0, 0), GridCoord::new(-1, 0, 0),
                GridCoord::new(0, 0, 1), GridCoord::new(0, 0, -1),
                GridCoord::new(1, 0, 1), GridCoord::new(1, 0, -1),
                GridCoord::new(-1, 0, 1), GridCoord::new(-1, 0, -1),
            ]);
        }
        if direction.z != 0 {
            perpendicular.extend_from_slice(&[
                GridCoord::new(1, 0, 0), GridCoord::new(-1, 0, 0),
                GridCoord::new(0, 1, 0), GridCoord::new(0, -1, 0),
                GridCoord::new(1, 1, 0), GridCoord::new(1, -1, 0),
                GridCoord::new(-1, 1, 0), GridCoord::new(-1, -1, 0),
            ]);
        }

        // 去重
        perpendicular.sort();
        perpendicular.dedup();
        perpendicular
    }

    /// 获取自然邻居（基于父节点方向）
    fn get_natural_neighbors(&self, current: &GridCoord, parent: &GridCoord) -> Vec<GridCoord> {
        let direction = GridCoord::new(
            (current.x - parent.x).signum(),
            (current.y - parent.y).signum(),
            (current.z - parent.z).signum(),
        );

        let mut natural = vec![direction];

        // 添加对角线方向
        if direction.x != 0 && direction.y != 0 {
            natural.push(GridCoord::new(direction.x, 0, 0));
            natural.push(GridCoord::new(0, direction.y, 0));
        }
        if direction.x != 0 && direction.z != 0 {
            natural.push(GridCoord::new(direction.x, 0, 0));
            natural.push(GridCoord::new(0, 0, direction.z));
        }
        if direction.y != 0 && direction.z != 0 {
            natural.push(GridCoord::new(0, direction.y, 0));
            natural.push(GridCoord::new(0, 0, direction.z));
        }

        natural
    }

    /// 获取所有可能的移动方向
    fn get_all_directions(&self) -> Vec<GridCoord> {
        vec![
            // 6个面方向
            GridCoord::new(1, 0, 0), GridCoord::new(-1, 0, 0),
            GridCoord::new(0, 1, 0), GridCoord::new(0, -1, 0),
            GridCoord::new(0, 0, 1), GridCoord::new(0, 0, -1),
            // 12个边方向
            GridCoord::new(1, 1, 0), GridCoord::new(1, -1, 0),
            GridCoord::new(-1, 1, 0), GridCoord::new(-1, -1, 0),
            GridCoord::new(1, 0, 1), GridCoord::new(1, 0, -1),
            GridCoord::new(-1, 0, 1), GridCoord::new(-1, 0, -1),
            GridCoord::new(0, 1, 1), GridCoord::new(0, 1, -1),
            GridCoord::new(0, -1, 1), GridCoord::new(0, -1, -1),
            // 8个角方向
            GridCoord::new(1, 1, 1), GridCoord::new(1, 1, -1),
            GridCoord::new(1, -1, 1), GridCoord::new(1, -1, -1),
            GridCoord::new(-1, 1, 1), GridCoord::new(-1, 1, -1),
            GridCoord::new(-1, -1, 1), GridCoord::new(-1, -1, -1),
        ]
    }

    /// 检查是否为对角线方向
    fn is_diagonal_direction(&self, direction: &GridCoord) -> bool {
        let non_zero_count = [direction.x, direction.y, direction.z]
            .iter()
            .filter(|&&x| x != 0)
            .count();
        non_zero_count > 1
    }

    /// 获取对角线方向的组成方向
    fn get_component_directions(&self, direction: &GridCoord) -> Vec<GridCoord> {
        let mut components = Vec::new();

        if direction.x != 0 {
            components.push(GridCoord::new(direction.x, 0, 0));
        }
        if direction.y != 0 {
            components.push(GridCoord::new(0, direction.y, 0));
        }
        if direction.z != 0 {
            components.push(GridCoord::new(0, 0, direction.z));
        }

        components
    }

    /// 检查点是否在粗粒度路径走廊内
    fn is_in_coarse_path_corridor(&self, coord: &GridCoord, coarse_path: &[GridCoord]) -> bool {
        let coarse_coord = self.config.to_coarse_coord(coord);

        // 检查是否在粗粒度路径上或其相邻区域
        for &path_coord in coarse_path {
            if coarse_coord == path_coord || coarse_coord.manhattan_distance(&path_coord) <= 1 {
                return true;
            }
        }
        false
    }

    /// 主要的路径规划函数
    pub fn find_path(&self, start: GridCoord, goal: GridCoord) -> PlanningResult {
        let start_time = Instant::now();

        // 验证输入
        if !self.config.is_valid_coord(&start) || !self.config.is_valid_coord(&goal) {
            return PlanningResult::InvalidInput;
        }

        if self.is_obstacle(&start) || self.is_obstacle(&goal) {
            return PlanningResult::NoPathFound;
        }

        if start == goal {
            let mut path = Path::new();
            path.waypoints = vec![start];
            path.total_cost = 0.0;
            path.planning_time_ms = start_time.elapsed().as_millis() as f32;
            return PlanningResult::Success(path);
        }

        // 第一阶段：粗粒度搜索
        let coarse_path = match self.coarse_astar(start, goal, start_time) {
            Some(path) => path,
            None => return PlanningResult::NoPathFound,
        };

        // 第二阶段：3D JPS细粒度搜索
        let fine_path = match self.fine_jps(start, goal, &coarse_path, start_time) {
            Some(path) => path,
            None => return PlanningResult::NoPathFound,
        };

        // 构建结果
        let mut result_path = Path::new();
        result_path.waypoints = fine_path;
        result_path.total_cost = result_path.calculate_distance();
        result_path.planning_time_ms = start_time.elapsed().as_millis() as f32;

        // 检查是否超时
        if result_path.planning_time_ms > self.max_planning_time_ms as f32 {
            PlanningResult::Timeout
        } else {
            PlanningResult::Success(result_path)
        }
    }

    /// 获取动态障碍物管理器的可变引用
    pub fn get_dynamic_obstacles_mut(&mut self) -> &mut DynamicObstacleManager {
        &mut self.dynamic_obstacles
    }

    /// 获取静态障碍物地图的可变引用
    pub fn get_static_obstacles_mut(&mut self) -> &mut StaticObstacleMap {
        &mut self.static_obstacles
    }
}
