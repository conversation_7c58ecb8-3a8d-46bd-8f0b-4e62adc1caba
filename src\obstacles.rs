use crate::types::{GridCoord, Point3D, BoundingBox, MapConfig};
use bit_vec::BitVec;
use serde::{Deserialize, Serialize};

/// 静态障碍物地图 - 使用位图优化内存
#[derive(Debug, Clone)]
pub struct StaticObstacleMap {
    obstacles: BitVec,
    config: MapConfig,
}

impl StaticObstacleMap {
    pub fn new(config: MapConfig) -> Self {
        let total_cells = (config.width as usize) * (config.height as usize) * (config.depth as usize);
        Self {
            obstacles: BitVec::from_elem(total_cells, false),
            config,
        }
    }

    /// 将3D坐标转换为1D索引
    fn coord_to_index(&self, coord: &GridCoord) -> Option<usize> {
        if !self.config.is_valid_coord(coord) {
            return None;
        }
        let index = (coord.z as usize) * (self.config.width as usize) * (self.config.height as usize) +
                   (coord.y as usize) * (self.config.width as usize) +
                   (coord.x as usize);
        Some(index)
    }

    /// 设置障碍物
    pub fn set_obstacle(&mut self, coord: GridCoord, is_obstacle: bool) {
        if let Some(index) = self.coord_to_index(&coord) {
            self.obstacles.set(index, is_obstacle);
        }
    }

    /// 检查是否为障碍物
    pub fn is_obstacle(&self, coord: &GridCoord) -> bool {
        self.coord_to_index(coord)
            .map(|index| self.obstacles[index])
            .unwrap_or(true) // 超出边界视为障碍物
    }

    /// 批量设置障碍物
    pub fn set_obstacles_batch(&mut self, coords: &[GridCoord], is_obstacle: bool) {
        for coord in coords {
            self.set_obstacle(*coord, is_obstacle);
        }
    }

    /// 设置矩形区域为障碍物
    pub fn set_box_obstacle(&mut self, bbox: &BoundingBox, is_obstacle: bool) {
        for x in bbox.min.x..=bbox.max.x {
            for y in bbox.min.y..=bbox.max.y {
                for z in bbox.min.z..=bbox.max.z {
                    self.set_obstacle(GridCoord::new(x, y, z), is_obstacle);
                }
            }
        }
    }

    /// 获取障碍物密度（用于启发式函数）
    pub fn get_obstacle_density(&self, center: &GridCoord, radius: i32) -> f32 {
        let mut total_cells = 0;
        let mut obstacle_cells = 0;

        for x in (center.x - radius)..=(center.x + radius) {
            for y in (center.y - radius)..=(center.y + radius) {
                for z in (center.z - radius)..=(center.z + radius) {
                    let coord = GridCoord::new(x, y, z);
                    if self.config.is_valid_coord(&coord) {
                        total_cells += 1;
                        if self.is_obstacle(&coord) {
                            obstacle_cells += 1;
                        }
                    }
                }
            }
        }

        if total_cells == 0 {
            0.0
        } else {
            obstacle_cells as f32 / total_cells as f32
        }
    }
}

/// 圆柱体禁飞区
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CylindricalNoFlyZone {
    pub center: Point3D,
    pub radius: f32,
    pub min_height: f32,
    pub max_height: f32,
    pub id: u32,
}

impl CylindricalNoFlyZone {
    pub fn new(center: Point3D, radius: f32, min_height: f32, max_height: f32, id: u32) -> Self {
        Self {
            center,
            radius,
            min_height,
            max_height,
            id,
        }
    }

    /// 检查点是否在禁飞区内
    pub fn contains_point(&self, point: &Point3D) -> bool {
        if point.z < self.min_height || point.z > self.max_height {
            return false;
        }

        let dx = point.x - self.center.x;
        let dy = point.y - self.center.y;
        let distance_sq = dx * dx + dy * dy;
        distance_sq <= self.radius * self.radius
    }

    /// 检查网格坐标是否在禁飞区内
    pub fn contains_grid(&self, coord: &GridCoord, grid_size: f32) -> bool {
        let world_point = coord.to_world(grid_size);
        self.contains_point(&world_point)
    }

    /// 获取禁飞区的边界框
    pub fn get_bounding_box(&self, grid_size: f32) -> BoundingBox {
        let min_coord = GridCoord::from_world(
            &Point3D::new(
                self.center.x - self.radius,
                self.center.y - self.radius,
                self.min_height,
            ),
            grid_size,
        );
        let max_coord = GridCoord::from_world(
            &Point3D::new(
                self.center.x + self.radius,
                self.center.y + self.radius,
                self.max_height,
            ),
            grid_size,
        );
        BoundingBox::new(min_coord, max_coord)
    }
}

/// 多边形禁飞区
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PolygonalNoFlyZone {
    pub vertices: Vec<Point3D>, // 2D多边形顶点（z坐标忽略）
    pub min_height: f32,
    pub max_height: f32,
    pub id: u32,
}

impl PolygonalNoFlyZone {
    pub fn new(vertices: Vec<Point3D>, min_height: f32, max_height: f32, id: u32) -> Self {
        Self {
            vertices,
            min_height,
            max_height,
            id,
        }
    }

    /// 使用射线投射算法检查点是否在多边形内
    pub fn contains_point(&self, point: &Point3D) -> bool {
        if point.z < self.min_height || point.z > self.max_height {
            return false;
        }

        let mut inside = false;
        let mut j = self.vertices.len() - 1;

        for i in 0..self.vertices.len() {
            let vi = &self.vertices[i];
            let vj = &self.vertices[j];

            if ((vi.y > point.y) != (vj.y > point.y)) &&
               (point.x < (vj.x - vi.x) * (point.y - vi.y) / (vj.y - vi.y) + vi.x) {
                inside = !inside;
            }
            j = i;
        }

        inside
    }

    /// 检查网格坐标是否在禁飞区内
    pub fn contains_grid(&self, coord: &GridCoord, grid_size: f32) -> bool {
        let world_point = coord.to_world(grid_size);
        self.contains_point(&world_point)
    }

    /// 获取禁飞区的边界框
    pub fn get_bounding_box(&self, grid_size: f32) -> BoundingBox {
        if self.vertices.is_empty() {
            return BoundingBox::new(GridCoord::new(0, 0, 0), GridCoord::new(0, 0, 0));
        }

        let mut min_x = self.vertices[0].x;
        let mut max_x = self.vertices[0].x;
        let mut min_y = self.vertices[0].y;
        let mut max_y = self.vertices[0].y;

        for vertex in &self.vertices {
            min_x = min_x.min(vertex.x);
            max_x = max_x.max(vertex.x);
            min_y = min_y.min(vertex.y);
            max_y = max_y.max(vertex.y);
        }

        let min_coord = GridCoord::from_world(
            &Point3D::new(min_x, min_y, self.min_height),
            grid_size,
        );
        let max_coord = GridCoord::from_world(
            &Point3D::new(max_x, max_y, self.max_height),
            grid_size,
        );
        BoundingBox::new(min_coord, max_coord)
    }
}

/// 动态障碍物管理器
#[derive(Debug)]
pub struct DynamicObstacleManager {
    cylindrical_zones: Vec<CylindricalNoFlyZone>,
    polygonal_zones: Vec<PolygonalNoFlyZone>,
    config: MapConfig,
}

impl DynamicObstacleManager {
    pub fn new(config: MapConfig) -> Self {
        Self {
            cylindrical_zones: Vec::new(),
            polygonal_zones: Vec::new(),
            config,
        }
    }

    /// 添加圆柱体禁飞区
    pub fn add_cylindrical_zone(&mut self, zone: CylindricalNoFlyZone) {
        self.cylindrical_zones.push(zone);
    }

    /// 添加多边形禁飞区
    pub fn add_polygonal_zone(&mut self, zone: PolygonalNoFlyZone) {
        self.polygonal_zones.push(zone);
    }

    /// 移除禁飞区
    pub fn remove_zone(&mut self, id: u32) {
        self.cylindrical_zones.retain(|zone| zone.id != id);
        self.polygonal_zones.retain(|zone| zone.id != id);
    }

    /// 检查坐标是否在任何禁飞区内
    pub fn is_in_no_fly_zone(&self, coord: &GridCoord) -> bool {
        // 检查圆柱体禁飞区
        for zone in &self.cylindrical_zones {
            if zone.contains_grid(coord, self.config.grid_size) {
                return true;
            }
        }

        // 检查多边形禁飞区
        for zone in &self.polygonal_zones {
            if zone.contains_grid(coord, self.config.grid_size) {
                return true;
            }
        }

        false
    }

    /// 获取与边界框相交的所有禁飞区
    pub fn get_intersecting_zones(&self, bbox: &BoundingBox) -> Vec<u32> {
        let mut intersecting_ids = Vec::new();

        for zone in &self.cylindrical_zones {
            let zone_bbox = zone.get_bounding_box(self.config.grid_size);
            if bbox.intersects(&zone_bbox) {
                intersecting_ids.push(zone.id);
            }
        }

        for zone in &self.polygonal_zones {
            let zone_bbox = zone.get_bounding_box(self.config.grid_size);
            if bbox.intersects(&zone_bbox) {
                intersecting_ids.push(zone.id);
            }
        }

        intersecting_ids
    }

    /// 清空所有禁飞区
    pub fn clear_all_zones(&mut self) {
        self.cylindrical_zones.clear();
        self.polygonal_zones.clear();
    }
}
