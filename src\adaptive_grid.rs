use crate::types::{GridCoord, BoundingBox, MapConfig};
use crate::obstacles::{StaticObstacleMap, DynamicObstacleManager};
use std::collections::HashMap;
use serde::{Deserialize, Serialize};

/// 自适应网格节点
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AdaptiveNode {
    pub bounds: BoundingBox,
    pub level: u8,           // 细分级别，0为最粗糙
    pub is_leaf: bool,       // 是否为叶子节点
    pub is_obstacle: bool,   // 是否为障碍物（仅叶子节点有效）
    pub children: Option<Box<[AdaptiveNode; 8]>>, // 八个子节点
    pub center: GridCoord,   // 节点中心坐标
    pub cost_multiplier: f32, // 通行代价倍数
}

impl AdaptiveNode {
    pub fn new(bounds: BoundingBox, level: u8) -> Self {
        let center = GridCoord::new(
            (bounds.min.x + bounds.max.x) / 2,
            (bounds.min.y + bounds.max.y) / 2,
            (bounds.min.z + bounds.max.z) / 2,
        );

        Self {
            bounds,
            level,
            is_leaf: true,
            is_obstacle: false,
            children: None,
            center,
            cost_multiplier: 1.0,
        }
    }

    /// 根据障碍物密度决定是否需要细分
    pub fn should_subdivide(&self, obstacle_map: &StaticObstacleMap, max_level: u8) -> bool {
        if self.level >= max_level {
            return false;
        }

        // 计算当前区域的障碍物密度
        let density = self.calculate_obstacle_density(obstacle_map);
        
        // 如果密度在中等范围，需要细分以获得更好的精度
        density > 0.1 && density < 0.9
    }

    /// 计算障碍物密度
    fn calculate_obstacle_density(&self, obstacle_map: &StaticObstacleMap) -> f32 {
        let mut total_cells = 0;
        let mut obstacle_cells = 0;
        
        // 采样检查，避免检查每个网格
        let step = ((self.bounds.max.x - self.bounds.min.x) / 8).max(1);
        
        for x in (self.bounds.min.x..=self.bounds.max.x).step_by(step as usize) {
            for y in (self.bounds.min.y..=self.bounds.max.y).step_by(step as usize) {
                for z in (self.bounds.min.z..=self.bounds.max.z).step_by(step as usize) {
                    let coord = GridCoord::new(x, y, z);
                    total_cells += 1;
                    if obstacle_map.is_obstacle(&coord) {
                        obstacle_cells += 1;
                    }
                }
            }
        }

        if total_cells == 0 {
            0.0
        } else {
            obstacle_cells as f32 / total_cells as f32
        }
    }

    /// 细分节点
    pub fn subdivide(&mut self, obstacle_map: &StaticObstacleMap, max_level: u8) {
        if !self.should_subdivide(obstacle_map, max_level) {
            return;
        }

        let mid_x = (self.bounds.min.x + self.bounds.max.x) / 2;
        let mid_y = (self.bounds.min.y + self.bounds.max.y) / 2;
        let mid_z = (self.bounds.min.z + self.bounds.max.z) / 2;

        let children = [
            // 8个子节点
            AdaptiveNode::new(
                BoundingBox::new(
                    self.bounds.min,
                    GridCoord::new(mid_x, mid_y, mid_z),
                ),
                self.level + 1,
            ),
            AdaptiveNode::new(
                BoundingBox::new(
                    GridCoord::new(mid_x + 1, self.bounds.min.y, self.bounds.min.z),
                    GridCoord::new(self.bounds.max.x, mid_y, mid_z),
                ),
                self.level + 1,
            ),
            AdaptiveNode::new(
                BoundingBox::new(
                    GridCoord::new(self.bounds.min.x, mid_y + 1, self.bounds.min.z),
                    GridCoord::new(mid_x, self.bounds.max.y, mid_z),
                ),
                self.level + 1,
            ),
            AdaptiveNode::new(
                BoundingBox::new(
                    GridCoord::new(mid_x + 1, mid_y + 1, self.bounds.min.z),
                    GridCoord::new(self.bounds.max.x, self.bounds.max.y, mid_z),
                ),
                self.level + 1,
            ),
            AdaptiveNode::new(
                BoundingBox::new(
                    GridCoord::new(self.bounds.min.x, self.bounds.min.y, mid_z + 1),
                    GridCoord::new(mid_x, mid_y, self.bounds.max.z),
                ),
                self.level + 1,
            ),
            AdaptiveNode::new(
                BoundingBox::new(
                    GridCoord::new(mid_x + 1, self.bounds.min.y, mid_z + 1),
                    GridCoord::new(self.bounds.max.x, mid_y, self.bounds.max.z),
                ),
                self.level + 1,
            ),
            AdaptiveNode::new(
                BoundingBox::new(
                    GridCoord::new(self.bounds.min.x, mid_y + 1, mid_z + 1),
                    GridCoord::new(mid_x, self.bounds.max.y, self.bounds.max.z),
                ),
                self.level + 1,
            ),
            AdaptiveNode::new(
                BoundingBox::new(
                    GridCoord::new(mid_x + 1, mid_y + 1, mid_z + 1),
                    self.bounds.max,
                ),
                self.level + 1,
            ),
        ];

        self.children = Some(Box::new(children));
        self.is_leaf = false;

        // 递归细分子节点
        if let Some(ref mut children) = self.children {
            for child in children.iter_mut() {
                child.subdivide(obstacle_map, max_level);
                
                // 设置叶子节点的障碍物状态
                if child.is_leaf {
                    let density = child.calculate_obstacle_density(obstacle_map);
                    child.is_obstacle = density > 0.5;
                    
                    // 根据密度设置通行代价
                    child.cost_multiplier = 1.0 + density * 2.0;
                }
            }
        }
    }

    /// 获取所有叶子节点
    pub fn get_leaf_nodes(&self) -> Vec<&AdaptiveNode> {
        if self.is_leaf {
            vec![self]
        } else if let Some(ref children) = self.children {
            children.iter().flat_map(|child| child.get_leaf_nodes()).collect()
        } else {
            vec![]
        }
    }

    /// 查找包含指定坐标的叶子节点
    pub fn find_leaf_containing(&self, coord: &GridCoord) -> Option<&AdaptiveNode> {
        if !self.bounds.contains(coord) {
            return None;
        }

        if self.is_leaf {
            Some(self)
        } else if let Some(ref children) = self.children {
            for child in children.iter() {
                if let Some(leaf) = child.find_leaf_containing(coord) {
                    return Some(leaf);
                }
            }
            None
        } else {
            None
        }
    }

    /// 获取相邻的叶子节点
    pub fn get_neighbors<'a>(&self, all_leaves: &[&'a AdaptiveNode]) -> Vec<&'a AdaptiveNode> {
        let mut neighbors = Vec::new();

        for &other in all_leaves {
            if std::ptr::eq(self, other) {
                continue;
            }

            // 检查是否相邻（边界相接或接近）
            if self.is_adjacent_to(other) {
                neighbors.push(other);
            }
        }

        neighbors
    }

    /// 检查两个节点是否相邻
    fn is_adjacent_to(&self, other: &AdaptiveNode) -> bool {
        let dx = (self.center.x - other.center.x).abs();
        let dy = (self.center.y - other.center.y).abs();
        let dz = (self.center.z - other.center.z).abs();
        
        // 考虑不同级别节点的大小差异
        let self_size = 1 << (3 - self.level); // 2^(3-level)
        let other_size = 1 << (3 - other.level);
        let max_distance = (self_size + other_size) / 2 + 1;
        
        dx <= max_distance && dy <= max_distance && dz <= max_distance &&
        (dx + dy + dz) <= max_distance * 2
    }
}

/// 自适应网格地图
#[derive(Debug)]
pub struct AdaptiveGridMap {
    root: AdaptiveNode,
    leaf_nodes: Vec<AdaptiveNode>,
    node_map: HashMap<GridCoord, usize>, // 中心坐标到叶子节点索引的映射
    config: MapConfig,
}

impl AdaptiveGridMap {
    pub fn new(config: MapConfig, obstacle_map: &StaticObstacleMap) -> Self {
        let bounds = BoundingBox::new(
            GridCoord::new(0, 0, 0),
            GridCoord::new(config.width - 1, config.height - 1, config.depth - 1),
        );

        let mut root = AdaptiveNode::new(bounds, 0);
        
        // 构建自适应网格，最大细分到4级
        root.subdivide(obstacle_map, 4);
        
        // 收集所有叶子节点
        let leaf_refs = root.get_leaf_nodes();
        let leaf_nodes: Vec<AdaptiveNode> = leaf_refs.into_iter().cloned().collect();
        
        // 构建坐标映射
        let mut node_map = HashMap::new();
        for (i, node) in leaf_nodes.iter().enumerate() {
            node_map.insert(node.center, i);
        }

        Self {
            root,
            leaf_nodes,
            node_map,
            config,
        }
    }

    /// 查找包含指定坐标的叶子节点
    pub fn find_node_at(&self, coord: &GridCoord) -> Option<&AdaptiveNode> {
        self.root.find_leaf_containing(coord)
    }

    /// 获取所有叶子节点
    pub fn get_all_nodes(&self) -> &[AdaptiveNode] {
        &self.leaf_nodes
    }

    /// 获取节点的邻居 - 返回索引而不是引用
    pub fn get_node_neighbor_indices(&self, node_center: &GridCoord) -> Vec<usize> {
        let mut neighbor_indices = Vec::new();

        // 找到当前节点
        let current_node_idx = if let Some(&idx) = self.node_map.get(node_center) {
            idx
        } else {
            return neighbor_indices;
        };

        let current_node = &self.leaf_nodes[current_node_idx];

        for (i, other_node) in self.leaf_nodes.iter().enumerate() {
            if i == current_node_idx {
                continue;
            }

            if current_node.is_adjacent_to(other_node) {
                neighbor_indices.push(i);
            }
        }

        neighbor_indices
    }

    /// 根据索引获取节点
    pub fn get_node_by_index(&self, index: usize) -> Option<&AdaptiveNode> {
        self.leaf_nodes.get(index)
    }

    /// 获取统计信息
    pub fn get_stats(&self) -> (usize, f32) {
        let total_nodes = self.leaf_nodes.len();
        let avg_level = self.leaf_nodes.iter().map(|n| n.level as f32).sum::<f32>() / total_nodes as f32;
        (total_nodes, avg_level)
    }
}
